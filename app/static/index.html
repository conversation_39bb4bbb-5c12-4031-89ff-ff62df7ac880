<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVE 搜索服务</title>

    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />

    <!-- React 和 Material-UI -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@mui/material@5.14.20/umd/material-ui.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- Markdown 和 DOMPurify -->
    <script src="https://unpkg.com/marked/marked.min.js"></script>
    <script src="https://unpkg.com/dompurify@2.4.7/dist/purify.min.js"></script>

    <!-- JSON 高亮库 -->
    <script src="https://unpkg.com/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://unpkg.com/prismjs@1.29.0/components/prism-json.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css">

    <!-- 本地样式 -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div id="app">
        <!-- React 应用将在这里渲染 -->
    </div>

    <!-- React 应用脚本 -->
    <script type="text/babel">
        const { useState, useEffect, useCallback, useRef } = React;
        const {
            ThemeProvider,
            createTheme,
            CssBaseline,
            Container,
            Typography,
            TextField,
            Button,
            Paper,
            Box,
            Grid,
            Chip,
            Card,
            CardContent,
            Pagination,
            CircularProgress,
            Collapse,
            IconButton,
            Checkbox,
            FormGroup,
            FormControlLabel,
            ButtonGroup,
            Fab,
            Snackbar,
            Alert,
            Badge,
            Accordion,
            AccordionSummary,
            AccordionDetails,
            InputAdornment,
            Stack,
            Divider
        } = MaterialUI;

        // 创建主题
        const theme = createTheme({
            palette: {
                primary: {
                    main: '#409eff',
                },
                secondary: {
                    main: '#67c23a',
                },
                background: {
                    default: '#f5f5f5',
                },
            },
            typography: {
                fontFamily: '"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif',
            },
        });

        // Markdown 渲染函数
        const renderMarkdown = (text) => {
            if (!text) return '';
            try {
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
                const html = marked.parse(text);
                return DOMPurify.sanitize(html);
            } catch (error) {
                console.error('Markdown 渲染错误:', error);
                return text.replace(/\n/g, '<br>');
            }
        };

        // 打字机效果组件 - 修复版本
        function TypewriterText({ text, speed = 20 }) {
            const [displayText, setDisplayText] = useState('');
            const textRef = useRef('');
            const indexRef = useRef(0);
            const timerRef = useRef(null);

            // 当text变化时，更新目标文本但不重置显示
            useEffect(() => {
                textRef.current = text;

                // 如果新文本比当前显示的文本长，继续打字机效果
                if (text.length > displayText.length) {
                    if (timerRef.current) {
                        clearTimeout(timerRef.current);
                    }

                    const typeNextChar = () => {
                        if (indexRef.current < textRef.current.length) {
                            setDisplayText(textRef.current.substring(0, indexRef.current + 1));
                            indexRef.current++;
                            timerRef.current = setTimeout(typeNextChar, speed);
                        }
                    };

                    // 如果当前没有在打字，开始打字
                    if (indexRef.current <= displayText.length) {
                        indexRef.current = displayText.length;
                        typeNextChar();
                    }
                }
            }, [text, speed]);

            // 清理定时器
            useEffect(() => {
                return () => {
                    if (timerRef.current) {
                        clearTimeout(timerRef.current);
                    }
                };
            }, []);

            return (
                <Box
                    sx={{ lineHeight: 1.6 }}
                    dangerouslySetInnerHTML={{ __html: renderMarkdown(displayText) }}
                />
            );
        }

        // CVE 搜索应用组件
        function CVESearchApp() {
            // 状态管理
            const [searchQuery, setSearchQuery] = useState('');
            const [pageSize, setPageSize] = useState(20);
            const [currentPage, setCurrentPage] = useState(1);
            const [searching, setSearching] = useState(false);
            const [hasSearched, setHasSearched] = useState(false);
            const [searchResults, setSearchResults] = useState(null);
            const [showExamples, setShowExamples] = useState(true);
            const [showFilters, setShowFilters] = useState(false);
            const [examples, setExamples] = useState([]);
            const [loadingSummaries, setLoadingSummaries] = useState({});
            const [streamingSummaries, setStreamingSummaries] = useState({});
            const [showBackToTop, setShowBackToTop] = useState(false);
            const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
            const [jsonModal, setJsonModal] = useState({ open: false, cveId: '', data: null, loading: false, wordWrap: true });

            // 筛选器状态
            const [selectedFilters, setSelectedFilters] = useState({
                vulnerability: [],
                software: [],
                language: [],
                severity: []
            });

            const [expandedGroups, setExpandedGroups] = useState({
                vulnerability: true,
                software: true,
                language: true,
                severity: true
            });

            const pageSizeOptions = [10, 20, 50, 100];

            // 筛选器选项
            const filterOptions = {
                vulnerability: [
                    { label: '缓冲区溢出', value: 'buffer_overflow', description: '包含堆栈或堆缓冲区溢出漏洞' },
                    { label: '任意代码执行', value: 'code_execution', description: '包含远程代码执行漏洞' },
                    { label: '任意文件读取', value: 'file_access', description: '包含路径遍历和文件包含漏洞' },
                    { label: 'SQL 注入', value: 'sql_injection', description: '包含 SQL 注入相关漏洞' },
                    { label: '跨站脚本(XSS)', value: 'xss', description: '包含跨站脚本攻击漏洞' },
                    { label: '跨站请求伪造(CSRF)', value: 'csrf', description: '包含跨站请求伪造漏洞' },
                    { label: '权限提升', value: 'privilege_escalation', description: '包含权限提升漏洞' },
                    { label: '信息泄露', value: 'information_disclosure', description: '包含信息泄露漏洞' }
                ],
                software: [
                    { label: '服务器软件', value: 'server', description: '包含 Apache、Nginx、Tomcat、IIS 等服务器软件' },
                    { label: '数据库', value: 'database', description: '包含 MySQL、PostgreSQL、MongoDB 等数据库' },
                    { label: '操作系统', value: 'os', description: '包含 Windows、Linux、macOS 等操作系统' },
                    { label: '浏览器', value: 'browser', description: '包含 Chrome、Firefox、Safari、Edge 等浏览器' }
                ],
                language: [
                    { label: 'PHP', value: 'php', description: '包含 PHP 相关漏洞' },
                    { label: 'Java', value: 'java', description: '包含 Java 相关漏洞' },
                    { label: 'Python', value: 'python', description: '包含 Python 相关漏洞' },
                    { label: 'JavaScript', value: 'javascript', description: '包含 JavaScript 相关漏洞' },
                    { label: 'C/C++', value: 'c_cpp', description: '包含 C/C++ 相关漏洞' },
                    { label: '.NET', value: 'dotnet', description: '包含 .NET 相关漏洞' }
                ],
                severity: [
                    { label: '严重 (Critical)', value: 'critical', description: '严重程度为 Critical 的漏洞' },
                    { label: '高危 (High)', value: 'high', description: '严重程度为 High 的漏洞' },
                    { label: '中危 (Medium)', value: 'medium', description: '严重程度为 Medium 的漏洞' },
                    { label: '低危 (Low)', value: 'low', description: '严重程度为 Low 的漏洞' }
                ]
            };

            // 工具函数
            const showSnackbar = (message, severity = 'info') => {
                setSnackbar({ open: true, message, severity });
            };

            const closeSnackbar = () => {
                setSnackbar(prev => ({ ...prev, open: false }));
            };

            // 加载搜索示例
            const loadExamples = useCallback(async () => {
                try {
                    const response = await fetch('/api/v1/examples');
                    const data = await response.json();
                    setExamples(data.examples);
                } catch (error) {
                    console.error('加载示例失败:', error);
                }
            }, []);

            // 构建筛选器查询
            const buildFilterQuery = useCallback(() => {
                const filterQueries = [];

                // 漏洞类型筛选
                if (selectedFilters.vulnerability.length > 0) {
                    const vulnQueries = [];
                    selectedFilters.vulnerability.forEach(filter => {
                        switch (filter) {
                            case 'buffer_overflow':
                                vulnQueries.push('"buffer overflow"', '"buffer-overflow"', '"buffer_overflow"');
                                break;
                            case 'sql_injection':
                                vulnQueries.push('+sql injection, +sql_injection, +SQLi');
                                break;
                            case 'code_execution':
                                vulnQueries.push('"remote code execution"', '"remote-code-execution"','" RCE "', '"(RCE)"');
                                break;
                            case 'file_access':
                                vulnQueries.push('+path traversal, +path_traversal, +LFI, +RFI');
                                break;
                            case 'xss':
                                vulnQueries.push('+cross site scripting, +cross-site-scripting, +XSS');
                                break;
                            case 'csrf':
                                vulnQueries.push('+cross site request forgery, +cross-site-request-forgery, +CSRF');
                                break;
                            case 'privilege_escalation':
                                vulnQueries.push('+privilege escalation, +privilege_escalation, +elevation');
                                break;
                            case 'information_disclosure':
                                vulnQueries.push('+information disclosure, +information_disclosure, +data leak');
                                break;
                        }
                    });
                    if (vulnQueries.length > 0) {
                        filterQueries.push(`(${vulnQueries.join(' | ')})`);
                    }
                }

                // 软件类型筛选
                if (selectedFilters.software.length > 0) {
                    const softwareQueries = [];
                    selectedFilters.software.forEach(filter => {
                        switch (filter) {
                            case 'server':
                                softwareQueries.push('+apache, +nginx, +tomcat, +iis');
                                break;
                            case 'database':
                                softwareQueries.push('+mysql, +mariadb, +postgresql, +mongodb');
                                break;
                            case 'os':
                                softwareQueries.push('+windows, +linux, +macos, +unix');
                                break;
                            case 'browser':
                                softwareQueries.push('+chrome, +firefox, +safari, +edge, +webkit');
                                break;
                        }
                    });
                    if (softwareQueries.length > 0) {
                        filterQueries.push(`(${softwareQueries.join(' | ')})`);
                    }
                }

                // 开发语言筛选
                if (selectedFilters.language.length > 0) {
                    const langQueries = [];
                    selectedFilters.language.forEach(filter => {
                        switch (filter) {
                            case 'php':
                                langQueries.push('+php');
                                break;
                            case 'java':
                                langQueries.push('+java');
                                break;
                            case 'python':
                                langQueries.push('+python');
                                break;
                            case 'javascript':
                                langQueries.push('+javascript, +nodejs, +node.js');
                                break;
                            case 'c_cpp':
                                langQueries.push('+c++, +cpp, +"c programming"');
                                break;
                            case 'dotnet':
                                langQueries.push('+.net, +dotnet, +asp.net');
                                break;
                        }
                    });
                    if (langQueries.length > 0) {
                        filterQueries.push(`(${langQueries.join(' | ')})`);
                    }
                }

                // 严重程度筛选
                if (selectedFilters.severity.length > 0) {
                    const severityQueries = [];
                    selectedFilters.severity.forEach(filter => {
                        switch (filter) {
                            case 'critical':
                                severityQueries.push('+critical');
                                break;
                            case 'high':
                                severityQueries.push('+high');
                                break;
                            case 'medium':
                                severityQueries.push('+medium');
                                break;
                            case 'low':
                                severityQueries.push('+low');
                                break;
                        }
                    });
                    if (severityQueries.length > 0) {
                        filterQueries.push(`(${severityQueries.join(' | ')})`);
                    }
                }

                return filterQueries.join(', ');
            }, [selectedFilters]);

            // 执行搜索
            const performSearch = useCallback(async (resetPage = true) => {
                const userQuery = searchQuery.trim();
                const filterQuery = buildFilterQuery();

                if (!userQuery && !filterQuery) {
                    showSnackbar('请输入搜索条件或选择筛选条件', 'warning');
                    return;
                }

                if (resetPage) {
                    setCurrentPage(1);
                }

                setSearching(true);
                setHasSearched(true);

                if (resetPage) {
                    setShowExamples(false);
                    setShowFilters(false);
                }

                try {
                    let fullQuery = '';
                    if (userQuery && filterQuery) {
                        fullQuery = `${userQuery}, ${filterQuery}`;
                    } else if (userQuery) {
                        fullQuery = userQuery;
                    } else {
                        fullQuery = filterQuery;
                    }

                    const params = new URLSearchParams({
                        query: fullQuery,
                        page: resetPage ? 1 : currentPage,
                        page_size: pageSize
                    });

                    const response = await fetch(`/api/v1/search?${params}`);

                    if (!response.ok) {
                        throw new Error(`搜索失败: ${response.statusText}`);
                    }

                    const data = await response.json();
                    // 确保数据结构完整
                    if (data && typeof data === 'object') {
                        data.results = data.results || [];
                        setSearchResults(data);
                    } else {
                        throw new Error('服务器返回的数据格式不正确');
                    }

                    if (resetPage) {
                        showSnackbar(`找到 ${data.total_results} 个结果`, 'success');
                    }

                } catch (error) {
                    console.error('搜索错误:', error);
                    showSnackbar(`搜索失败: ${error.message}`, 'error');
                } finally {
                    setSearching(false);
                }
            }, [searchQuery, buildFilterQuery, currentPage, pageSize]);

            // 工具函数
            const formatDate = (dateStr) => {
                if (!dateStr) return '未知';
                try {
                    return new Date(dateStr).toLocaleDateString('zh-CN');
                } catch {
                    return dateStr;
                }
            };

            const truncateText = (text, maxLength = 500) => {
                if (!text) return '';
                if (text.length <= maxLength) return text;
                return text.substring(0, maxLength) + '...';
            };

            const getSeverityColor = (severity) => {
                const severityMap = {
                    'CRITICAL': 'error',
                    'HIGH': 'error',
                    'MEDIUM': 'warning',
                    'LOW': 'info'
                };
                return severityMap[severity?.toUpperCase()] || 'default';
            };



            // 生成 AI 总结（流式）
            const generateSummary = async (cveId) => {
                setLoadingSummaries(prev => ({ ...prev, [cveId]: true }));
                setStreamingSummaries(prev => ({ ...prev, [cveId]: '' }));

                try {
                    const response = await fetch(`/api/v1/summary/${cveId}/stream`);

                    if (!response.ok) {
                        throw new Error(`生成总结失败: ${response.statusText}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    let isFromCache = false;

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    if (data.type === 'cache') {
                                        // 命中缓存，直接显示完整总结
                                        isFromCache = true;
                                        const summary = data.data;

                                        // 更新搜索结果中的总结
                                        if (searchResults && searchResults.results && Array.isArray(searchResults.results)) {
                                            const updatedResults = searchResults.results.map(result =>
                                                result.cve_info.cve_id === cveId ? { ...result, summary } : result
                                            );
                                            setSearchResults(prev => prev ? { ...prev, results: updatedResults } : null);
                                        }

                                        showSnackbar('从缓存获取 AI 总结', 'info');
                                        break;

                                    } else if (data.type === 'chunk') {
                                        // 流式数据，累积显示
                                        setStreamingSummaries(prev => ({
                                            ...prev,
                                            [cveId]: (prev[cveId] || '') + data.data
                                        }));

                                    } else if (data.type === 'complete') {
                                        // 流式完成，更新最终结果
                                        const summary = data.data;

                                        // 更新搜索结果中的总结
                                        if (searchResults && searchResults.results && Array.isArray(searchResults.results)) {
                                            const updatedResults = searchResults.results.map(result =>
                                                result.cve_info.cve_id === cveId ? { ...result, summary } : result
                                            );
                                            setSearchResults(prev => prev ? { ...prev, results: updatedResults } : null);
                                        }

                                        // 清除流式状态
                                        setStreamingSummaries(prev => {
                                            const newState = { ...prev };
                                            delete newState[cveId];
                                            return newState;
                                        });

                                        showSnackbar('AI 总结生成成功', 'success');

                                    } else if (data.type === 'error') {
                                        throw new Error(data.data);
                                    }
                                } catch (parseError) {
                                    console.error('解析流式数据错误:', parseError);
                                }
                            }
                        }

                        if (isFromCache) break;
                    }

                } catch (error) {
                    console.error('生成总结错误:', error);
                    showSnackbar(`生成总结失败: ${error.message}`, 'error');

                    // 清除流式状态
                    setStreamingSummaries(prev => {
                        const newState = { ...prev };
                        delete newState[cveId];
                        return newState;
                    });
                } finally {
                    setLoadingSummaries(prev => ({ ...prev, [cveId]: false }));
                }
            };

            // 刷新 AI 总结（流式）
            const refreshSummary = async (cveId) => {
                setLoadingSummaries(prev => ({ ...prev, [cveId]: true }));
                setStreamingSummaries(prev => ({ ...prev, [cveId]: '' }));

                try {
                    const response = await fetch(`/api/v1/summary/${cveId}/stream?force_refresh=true`);

                    if (!response.ok) {
                        throw new Error(`刷新总结失败: ${response.statusText}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    if (data.type === 'chunk') {
                                        // 流式数据，累积显示
                                        setStreamingSummaries(prev => ({
                                            ...prev,
                                            [cveId]: (prev[cveId] || '') + data.data
                                        }));

                                    } else if (data.type === 'complete') {
                                        // 流式完成，更新最终结果
                                        const summary = data.data;

                                        // 更新搜索结果中的总结
                                        if (searchResults && searchResults.results && Array.isArray(searchResults.results)) {
                                            const updatedResults = searchResults.results.map(result =>
                                                result.cve_info.cve_id === cveId ? { ...result, summary } : result
                                            );
                                            setSearchResults(prev => prev ? { ...prev, results: updatedResults } : null);
                                        }

                                        // 清除流式状态
                                        setStreamingSummaries(prev => {
                                            const newState = { ...prev };
                                            delete newState[cveId];
                                            return newState;
                                        });

                                        showSnackbar('AI 总结已刷新', 'success');

                                    } else if (data.type === 'error') {
                                        throw new Error(data.data);
                                    }
                                } catch (parseError) {
                                    console.error('解析流式数据错误:', parseError);
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error('刷新总结错误:', error);
                    showSnackbar(`刷新总结失败: ${error.message}`, 'error');

                    // 清除流式状态
                    setStreamingSummaries(prev => {
                        const newState = { ...prev };
                        delete newState[cveId];
                        return newState;
                    });
                } finally {
                    setLoadingSummaries(prev => ({ ...prev, [cveId]: false }));
                }
            };

            // 获取 CVE 原始 JSON 数据
            const fetchCveRawData = async (cveId) => {
                setJsonModal(prev => ({ ...prev, open: true, cveId, loading: true, data: null }));

                try {
                    const response = await fetch(`/api/v1/cve/${cveId}/raw`);

                    if (!response.ok) {
                        throw new Error(`获取原始数据失败: ${response.statusText}`);
                    }

                    const data = await response.json();
                    setJsonModal(prev => ({ ...prev, data: data.raw_data, loading: false }));

                } catch (error) {
                    console.error('获取原始数据错误:', error);
                    showSnackbar(`获取原始数据失败: ${error.message}`, 'error');
                    setJsonModal(prev => ({ ...prev, open: false, loading: false }));
                }
            };

            // 关闭 JSON 模态框
            const closeJsonModal = () => {
                setJsonModal({ open: false, cveId: '', data: null, loading: false, wordWrap: false });
            };

            // 切换自动换行模式
            const toggleWordWrap = () => {
                setJsonModal(prev => ({ ...prev, wordWrap: !prev.wordWrap }));
            };

            // 格式化 JSON 数据
            const formatJsonData = (data) => {
                if (!data) return '';
                try {
                    return JSON.stringify(data, null, 2);
                } catch (error) {
                    console.error('JSON 格式化错误:', error);
                    return JSON.stringify(data);
                }
            };

            // JSON 语法高亮
            const highlightJson = (jsonString) => {
                if (!jsonString) return '';

                return jsonString
                    .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                        let cls = 'json-number';
                        if (/^"/.test(match)) {
                            if (/:$/.test(match)) {
                                cls = 'json-key';
                            } else {
                                cls = 'json-string';
                            }
                        } else if (/true|false/.test(match)) {
                            cls = 'json-boolean';
                        } else if (/null/.test(match)) {
                            cls = 'json-null';
                        }
                        return '<span class="' + cls + '">' + match + '</span>';
                    })
                    .replace(/([{}[\],])/g, '<span class="json-punctuation">$1</span>');
            };

            // 滚动监听
            useEffect(() => {
                const handleScroll = () => {
                    setShowBackToTop(window.scrollY > 300);
                };
                window.addEventListener('scroll', handleScroll);
                return () => window.removeEventListener('scroll', handleScroll);
            }, []);

            // 加载示例
            useEffect(() => {
                loadExamples();
                setShowExamples(!hasSearched);
            }, [loadExamples, hasSearched]);

            return (
                <ThemeProvider theme={theme}>
                    <CssBaseline />
                    <div className="app-container">
                        {/* 头部 */}
                        <div className="header">
                            <Typography variant="h2" component="h1" sx={{ color: 'white', textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
                                🔍 CVE 搜索服务
                            </Typography>
                            <Typography variant="h6" sx={{ color: 'white', opacity: 0.9, mt: 1 }}>
                                基于 AI 的漏洞信息搜索与总结平台
                            </Typography>
                        </div>

                        {/* 搜索容器 */}
                        <Container maxWidth="lg" sx={{ mt: 4 }}>
                            <Paper elevation={3} sx={{ borderRadius: '20px', overflow: 'hidden' }}>
                                {/* 搜索区域 */}
                                <Box sx={{ p: 4, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        placeholder="输入搜索条件，例如：remote code execution"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && performSearch()}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <span className="material-icons">edit</span>
                                                </InputAdornment>
                                            ),
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <Button
                                                        variant="contained"
                                                        onClick={() => performSearch()}
                                                        disabled={searching}
                                                        startIcon={searching ? <CircularProgress size={20} /> : null}
                                                    >
                                                        搜索
                                                    </Button>
                                                </InputAdornment>
                                            ),
                                        }}
                                        sx={{ mb: 2 }}
                                    />

                                    {/* 搜索选项 */}
                                    <Paper sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.8)' }}>
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                <Typography variant="body2">每页显示：</Typography>
                                                <ButtonGroup size="small">
                                                    {pageSizeOptions.map(size => (
                                                        <Button
                                                            key={size}
                                                            variant={pageSize === size ? 'contained' : 'outlined'}
                                                            onClick={() => {
                                                                setPageSize(size);
                                                                setCurrentPage(1);
                                                                if (hasSearched && searchQuery.trim()) {
                                                                    performSearch(false);
                                                                }
                                                            }}
                                                        >
                                                            {size}
                                                        </Button>
                                                    ))}
                                                </ButtonGroup>
                                            </Box>

                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Button
                                                    size="small"
                                                    variant={showFilters ? 'contained' : 'outlined'}
                                                    onClick={() => setShowFilters(!showFilters)}
                                                    startIcon={<span className="material-icons">{showFilters ? 'expand_less' : 'expand_more'}</span>}
                                                    endIcon={
                                                        Object.values(selectedFilters).reduce((total, group) => total + group.length, 0) > 0 ? (
                                                            <Badge badgeContent={Object.values(selectedFilters).reduce((total, group) => total + group.length, 0)} color="primary" />
                                                        ) : null
                                                    }
                                                >
                                                    {showFilters ? '隐藏' : '显示'}筛选器
                                                </Button>

                                                <Button
                                                    size="small"
                                                    variant={showExamples ? 'contained' : 'outlined'}
                                                    onClick={() => setShowExamples(!showExamples)}
                                                    startIcon={<span className="material-icons">{showExamples ? 'expand_less' : 'expand_more'}</span>}
                                                >
                                                    {showExamples ? '隐藏' : '显示'}搜索示例
                                                </Button>
                                            </Box>
                                        </Box>
                                    </Paper>

                                    {/* 筛选器面板 */}
                                    <Collapse in={showFilters}>
                                        <Box sx={{ mt: 2, p: 3, backgroundColor: 'rgba(255, 255, 255, 0.95)', borderRadius: 2 }}>
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                                <Typography variant="h6" sx={{ color: '#409eff', display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    🎯 快速筛选
                                                </Typography>
                                                {Object.values(selectedFilters).reduce((total, group) => total + group.length, 0) > 0 && (
                                                    <Button
                                                        size="small"
                                                        color="error"
                                                        onClick={() => setSelectedFilters({
                                                            vulnerability: [],
                                                            software: [],
                                                            language: [],
                                                            severity: []
                                                        })}
                                                    >
                                                        清空筛选
                                                    </Button>
                                                )}
                                            </Box>

                                            <Grid container spacing={2}>
                                                {Object.entries(filterOptions).map(([groupName, options]) => (
                                                    <Grid item xs={12} md={6} key={groupName}>
                                                        <Accordion defaultExpanded={expandedGroups[groupName]}>
                                                            <AccordionSummary
                                                                expandIcon={<span className="material-icons">expand_more</span>}
                                                                onClick={() => setExpandedGroups(prev => ({ ...prev, [groupName]: !prev[groupName] }))}
                                                            >
                                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                                    <span className="material-icons">
                                                                        {groupName === 'vulnerability' ? 'warning' :
                                                                         groupName === 'software' ? 'settings' :
                                                                         groupName === 'language' ? 'code' : 'security'}
                                                                    </span>
                                                                    <Typography>
                                                                        {groupName === 'vulnerability' ? '漏洞类型' :
                                                                         groupName === 'software' ? '软件类型' :
                                                                         groupName === 'language' ? '开发语言' : '严重程度'}
                                                                    </Typography>
                                                                    {selectedFilters[groupName].length > 0 && (
                                                                        <Badge badgeContent={selectedFilters[groupName].length} color="primary" sx={{ ml: 1 }} />
                                                                    )}
                                                                </Box>
                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <FormGroup>
                                                                    {options.map(option => (
                                                                        <FormControlLabel
                                                                            key={option.value}
                                                                            control={
                                                                                <Checkbox
                                                                                    checked={selectedFilters[groupName].includes(option.value)}
                                                                                    onChange={(e) => {
                                                                                        const isChecked = e.target.checked;
                                                                                        setSelectedFilters(prev => ({
                                                                                            ...prev,
                                                                                            [groupName]: isChecked
                                                                                                ? [...prev[groupName], option.value]
                                                                                                : prev[groupName].filter(v => v !== option.value)
                                                                                        }));
                                                                                    }}
                                                                                />
                                                                            }
                                                                            label={option.label}
                                                                            title={option.description}
                                                                        />
                                                                    ))}
                                                                </FormGroup>
                                                            </AccordionDetails>
                                                        </Accordion>
                                                    </Grid>
                                                ))}
                                            </Grid>
                                        </Box>
                                    </Collapse>

                                    {/* 搜索示例 */}
                                    <Collapse in={showExamples}>
                                        <Box sx={{ mt: 2, p: 3, backgroundColor: 'rgba(255, 255, 255, 0.95)', borderRadius: 2 }}>
                                            <Typography variant="h6" sx={{ mb: 2, color: '#409eff' }}>
                                                🎯 搜索语法示例
                                            </Typography>
                                            {examples.map((example, index) => (
                                                <Card
                                                    key={index}
                                                    sx={{
                                                        mb: 2,
                                                        cursor: 'pointer',
                                                        transition: 'all 0.3s',
                                                        '&:hover': {
                                                            transform: 'translateX(5px)',
                                                            backgroundColor: '#e3f2fd'
                                                        }
                                                    }}
                                                    onClick={() => {
                                                        setSearchQuery(example.query);
                                                        setShowExamples(false);
                                                    }}
                                                >
                                                    <CardContent>
                                                        <Typography variant="subtitle1" sx={{ color: '#409eff', fontWeight: 'bold' }}>
                                                            {example.title}
                                                        </Typography>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                fontFamily: 'monospace',
                                                                backgroundColor: '#fff',
                                                                p: 1,
                                                                border: '1px solid #ddd',
                                                                borderRadius: 1,
                                                                my: 1
                                                            }}
                                                        >
                                                            {example.query}
                                                        </Typography>
                                                        <Typography variant="body2" color="text.secondary">
                                                            {example.description}
                                                        </Typography>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </Box>
                                    </Collapse>
                                </Box>

                                {/* 搜索结果 */}
                                {hasSearched && (
                                    <Box sx={{ p: 4 }}>
                                        {searching ? (
                                            <Box sx={{ textAlign: 'center', p: 4 }}>
                                                <CircularProgress size={40} />
                                                <Typography sx={{ mt: 2 }}>正在搜索中...</Typography>
                                            </Box>
                                        ) : searchResults ? (
                                            <>
                                                {/* 搜索统计 */}
                                                <Box sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: 2, mb: 2 }}>
                                                    <Grid container justifyContent="space-between" alignItems="center">
                                                        <Grid item xs={12} md={8}>
                                                            <Stack direction="row" spacing={1} flexWrap="wrap">
                                                                <Chip
                                                                    icon={<span className="material-icons">check_circle</span>}
                                                                    label={`共找到 ${searchResults.total_results} 个 CVE`}
                                                                    color="success"
                                                                    size="medium"
                                                                />
                                                                <Chip
                                                                    label={`第 ${searchResults.page} / ${searchResults.total_pages} 页`}
                                                                    color="info"
                                                                />
                                                                <Chip
                                                                    label={`显示 ${(searchResults.page - 1) * searchResults.page_size + 1}-${Math.min(searchResults.page * searchResults.page_size, searchResults.total_results)}`}
                                                                    color="warning"
                                                                />
                                                            </Stack>
                                                        </Grid>
                                                        <Grid item xs={12} md={4} sx={{ textAlign: { xs: 'left', md: 'right' }, mt: { xs: 1, md: 0 } }}>
                                                            <Chip
                                                                label={`搜索耗时: ${searchResults.search_time?.toFixed(2) || 0} 秒`}
                                                                color="info"
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </Box>

                                                {/* 顶部分页 */}
                                                {searchResults.total_pages > 1 && (
                                                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                                                        <Pagination
                                                            count={searchResults.total_pages}
                                                            page={currentPage}
                                                            onChange={(event, page) => {
                                                                setCurrentPage(page);
                                                                performSearch(false);
                                                                window.scrollTo({ top: 0, behavior: 'smooth' });
                                                            }}
                                                            color="primary"
                                                            size="large"
                                                        />
                                                    </Box>
                                                )}

                                                {/* 结果列表 */}
                                                {searchResults && searchResults.results && Array.isArray(searchResults.results) && searchResults.results.length > 0 ? (
                                                    <>
                                                        {searchResults.results.map((result) => (
                                                            <Card
                                                                key={result.cve_info.cve_id}
                                                                sx={{
                                                                    mb: 3,
                                                                    p: 3,
                                                                    transition: 'all 0.3s',
                                                                    '&:hover': {
                                                                        boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                                                                        transform: 'translateY(-2px)'
                                                                    }
                                                                }}
                                                            >
                                                                <CardContent>
                                                                    {/* CVE 头部信息 */}
                                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
                                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                                            <Typography variant="h5" sx={{ color: '#e74c3c', fontWeight: 'bold' }}>
                                                                                {result.cve_info.cve_id}
                                                                            </Typography>
                                                                            <Button
                                                                                size="small"
                                                                                variant="outlined"
                                                                                color="primary"
                                                                                onClick={() => fetchCveRawData(result.cve_info.cve_id)}
                                                                                startIcon={<span className="material-icons">code</span>}
                                                                                sx={{
                                                                                    minWidth: 'auto',
                                                                                    borderRadius: '20px',
                                                                                    textTransform: 'none'
                                                                                }}
                                                                            >
                                                                                查看详情
                                                                            </Button>
                                                                        </Box>
                                                                        <Typography variant="body2" color="text.secondary">
                                                                            {formatDate(result.cve_info.published_date)}
                                                                        </Typography>
                                                                    </Box>

                                                                    {/* CVE 标题 */}
                                                                    {result.cve_info.title && (
                                                                        <Typography variant="h6" sx={{ mb: 1, color: '#2c3e50', fontWeight: 600 }}>
                                                                            {result.cve_info.title}
                                                                        </Typography>
                                                                    )}

                                                                    {/* CVE 描述 */}
                                                                    {result.cve_info.description && (
                                                                        <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6, color: '#555' }}>
                                                                            {truncateText(result.cve_info.description, 1000)}
                                                                        </Typography>
                                                                    )}

                                                                    {/* CVE 元信息 */}
                                                                    <Stack direction="row" spacing={2} flexWrap="wrap" sx={{ mb: 2 }}>
                                                                        {result.cve_info.severity && (
                                                                            <Chip
                                                                                label={result.cve_info.severity}
                                                                                color={getSeverityColor(result.cve_info.severity)}
                                                                            />
                                                                        )}
                                                                        {result.cve_info.cvss_score && (
                                                                            <Chip
                                                                                label={`CVSS: ${result.cve_info.cvss_score}`}
                                                                                color="warning"
                                                                            />
                                                                        )}
                                                                        {result.cve_info.affected_products && result.cve_info.affected_products.length > 0 && (
                                                                            <Chip
                                                                                label={`影响产品: ${result.cve_info.affected_products.slice(0, 3).join(' | ')}${result.cve_info.affected_products.length > 3 ? '...' : ''}`}
                                                                                color="info"
                                                                            />
                                                                        )}
                                                                    </Stack>

                                                                    {/* AI 总结区域 */}
                                                                    {result.summary || streamingSummaries[result.cve_info.cve_id] ? (
                                                                        <Paper sx={{ p: 2, backgroundColor: '#e8f5e8', border: '1px solid #4caf50', borderRadius: 2, mt: 2 }}>
                                                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                                                    <span className="material-icons" style={{ color: '#2e7d32' }}>chat</span>
                                                                                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                                                                                        AI 总结
                                                                                    </Typography>
                                                                                    {result.summary && (
                                                                                        <Chip size="small" label={result.summary.model_used} color="success" />
                                                                                    )}
                                                                                    {streamingSummaries[result.cve_info.cve_id] && (
                                                                                        <Chip size="small" label="生成中..." color="warning" />
                                                                                    )}
                                                                                </Box>
                                                                                <Button
                                                                                    size="small"
                                                                                    variant="contained"
                                                                                    onClick={() => refreshSummary(result.cve_info.cve_id)}
                                                                                    disabled={loadingSummaries[result.cve_info.cve_id]}
                                                                                    startIcon={loadingSummaries[result.cve_info.cve_id] ? <CircularProgress size={16} /> : null}
                                                                                >
                                                                                    刷新总结
                                                                                </Button>
                                                                            </Box>

                                                                            {/* 显示流式内容或最终内容 */}
                                                                            {streamingSummaries[result.cve_info.cve_id] ? (
                                                                                <TypewriterText
                                                                                    text={streamingSummaries[result.cve_info.cve_id]}
                                                                                    speed={20}
                                                                                />
                                                                            ) : result.summary ? (
                                                                                <Box
                                                                                    sx={{ lineHeight: 1.6 }}
                                                                                    dangerouslySetInnerHTML={{ __html: renderMarkdown(result.summary.summary) }}
                                                                                />
                                                                            ) : null}
                                                                        </Paper>
                                                                    ) : (
                                                                        <Paper sx={{ p: 2, backgroundColor: '#f5f5f5', border: '1px solid #ddd', borderRadius: 2, mt: 2, textAlign: 'center' }}>
                                                                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                                                暂无 AI 总结
                                                                            </Typography>
                                                                            <Button
                                                                                variant="contained"
                                                                                onClick={() => generateSummary(result.cve_info.cve_id)}
                                                                                disabled={loadingSummaries[result.cve_info.cve_id]}
                                                                                startIcon={loadingSummaries[result.cve_info.cve_id] ? <CircularProgress size={16} /> : <span className="material-icons">chat</span>}
                                                                            >
                                                                                生成 AI 总结
                                                                            </Button>
                                                                        </Paper>
                                                                    )}
                                                                </CardContent>
                                                            </Card>
                                                        ))}

                                                        {/* 底部分页 */}
                                                        {searchResults.total_pages > 1 && (
                                                            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                                                                <Pagination
                                                                    count={searchResults.total_pages}
                                                                    page={currentPage}
                                                                    onChange={(event, page) => {
                                                                        setCurrentPage(page);
                                                                        performSearch(false);
                                                                        window.scrollTo({ top: 0, behavior: 'smooth' });
                                                                    }}
                                                                    color="primary"
                                                                    size="large"
                                                                />
                                                            </Box>
                                                        )}
                                                    </>
                                                ) : (
                                                    <Box sx={{ textAlign: 'center', p: 4 }}>
                                                        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                                                            未找到匹配的 CVE
                                                        </Typography>
                                                        <Button variant="contained" onClick={() => setShowExamples(true)}>
                                                            查看搜索示例
                                                        </Button>
                                                    </Box>
                                                )}
                                            </>
                                        ) : null}
                                    </Box>
                                )}
                            </Paper>
                        </Container>

                        {/* 回到顶部按钮 */}
                        {showBackToTop && (
                            <Fab
                                color="primary"
                                sx={{
                                    position: 'fixed',
                                    bottom: 30,
                                    right: 30,
                                    zIndex: 1000
                                }}
                                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                            >
                                <span className="material-icons">keyboard_arrow_up</span>
                            </Fab>
                        )}

                        {/* Snackbar */}
                        <Snackbar
                            open={snackbar.open}
                            autoHideDuration={6000}
                            onClose={closeSnackbar}
                        >
                            <Alert onClose={closeSnackbar} severity={snackbar.severity}>
                                {snackbar.message}
                            </Alert>
                        </Snackbar>

                        {/* JSON 模态框 */}
                        {jsonModal.open && (
                            <div className="json-modal" onClick={(e) => e.target.className === 'json-modal' && closeJsonModal()}>
                                <div className="json-modal-content">
                                    <div className="json-modal-header">
                                        <h3>CVE 详细信息 - {jsonModal.cveId}</h3>
                                        <div className="json-modal-controls">
                                            <button
                                                className={`json-wrap-toggle ${jsonModal.wordWrap ? 'active' : ''}`}
                                                onClick={toggleWordWrap}
                                                title={jsonModal.wordWrap ? '禁用自动换行' : '启用自动换行'}
                                            >
                                                <span className="material-icons">
                                                    {jsonModal.wordWrap ? 'wrap_text' : 'format_align_left'}
                                                </span>
                                                {jsonModal.wordWrap ? '禁用换行' : '自动换行'}
                                            </button>
                                            <button className="json-modal-close" onClick={closeJsonModal}>
                                                ×
                                            </button>
                                        </div>
                                    </div>
                                    <div className="json-modal-body">
                                        {jsonModal.loading ? (
                                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', color: 'white' }}>
                                                <CircularProgress color="inherit" />
                                                <Typography sx={{ ml: 2, color: 'white' }}>加载中...</Typography>
                                            </Box>
                                        ) : jsonModal.data ? (
                                            <div className="json-viewer">
                                                <pre
                                                    style={{
                                                        margin: 0,
                                                        padding: '20px',
                                                        background: '#1e1e1e',
                                                        color: '#d4d4d4',
                                                        fontSize: '14px',
                                                        lineHeight: '1.4',
                                                        overflow: 'auto',
                                                        height: '100%',
                                                        fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
                                                        whiteSpace: jsonModal.wordWrap ? 'pre-wrap' : 'pre',
                                                        wordBreak: jsonModal.wordWrap ? 'break-word' : 'normal'
                                                    }}
                                                    dangerouslySetInnerHTML={{
                                                        __html: highlightJson(formatJsonData(jsonModal.data))
                                                    }}
                                                />
                                            </div>
                                        ) : (
                                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', color: 'white' }}>
                                                <Typography sx={{ color: 'white' }}>无法加载数据</Typography>
                                            </Box>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </ThemeProvider>
            );
        }

        // 渲染应用
        ReactDOM.render(<CVESearchApp />, document.getElementById('app'));
    </script>
</body>
</html>
