# 多阶段构建 Dockerfile
# 第一阶段：构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# 复制前端源码
COPY ../frontend/package*.json ./
RUN npm ci --only=production

COPY ../frontend/ ./
RUN npm run build

# 第二阶段：构建后端
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制后端依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端源码
COPY app/ ./app/
COPY config/ ./config/

# 从前端构建阶段复制构建产物（如果需要覆盖）
# COPY --from=frontend-builder /app/frontend/dist ./app/static

# 创建必要的目录
RUN mkdir -p /app/cache /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
