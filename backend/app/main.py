"""
FastAPI 主应用
CVE 搜索服务的入口点
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware

from .core.config import settings
from .core.database import init_database, close_database
from .api.routes import router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    print("初始化数据库...")
    await init_database()
    print("数据库初始化完成")
    
    # 检查 CVE 目录
    if not os.path.exists(settings.search.cve_directory):
        print(f"警告: CVE 目录不存在: {settings.search.cve_directory}")
        print("请确保 CVE 数据库目录存在并包含 JSON 文件")
    else:
        print(f"CVE 目录: {settings.search.cve_directory}")
    
    yield
    
    # 关闭时清理资源
    print("关闭数据库连接...")
    await close_database()
    print("应用关闭完成")


# 创建 FastAPI 应用
app = FastAPI(
    title="CVE 搜索服务",
    description="基于 FastAPI 的 CVE 搜索服务，提供现代化的 Web 界面和 LLM 总结功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册 API 路由
app.include_router(router)

# 挂载静态文件
static_dir = os.path.join(os.path.dirname(__file__), "static")
if os.path.exists(static_dir):
    # 挂载 assets 目录到 /assets 路径
    assets_dir = os.path.join(static_dir, "assets")
    if os.path.exists(assets_dir):
        app.mount("/assets", StaticFiles(directory=assets_dir), name="assets")

    # 挂载其他静态文件到根路径（用于 favicon 等）
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


@app.get("/", summary="主页")
async def read_root():
    """
    返回主页 HTML 文件
    """
    static_dir = os.path.join(os.path.dirname(__file__), "static")
    index_file = os.path.join(static_dir, "index.html")
    
    if os.path.exists(index_file):
        return FileResponse(index_file)
    else:
        return {
            "message": "CVE 搜索服务",
            "version": "1.0.0",
            "docs": "/docs",
            "api": "/api"
        }


@app.get("/favicon.ico")
async def favicon():
    """返回网站图标"""
    favicon_path = os.path.join(os.path.dirname(__file__), "static", "favicon.ico")
    if os.path.exists(favicon_path):
        return FileResponse(favicon_path)
    else:
        # 返回一个简单的 204 No Content 响应
        from fastapi import Response
        return Response(status_code=204)


@app.get("/vite.svg")
async def vite_svg():
    """返回 Vite logo"""
    vite_path = os.path.join(os.path.dirname(__file__), "static", "vite.svg")
    if os.path.exists(vite_path):
        return FileResponse(vite_path, media_type="image/svg+xml")
    else:
        from fastapi import Response
        return Response(status_code=204)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.debug
    )
