import{u as Xe,i as R,j as e,C as Y,B as f,T as h,a as Se,b as Ce,c as $,G as q,A as Ye,d as Ze,e as ke,f as er,F as rr,g as $e,h as sr,S as F,k,I as U,D as ge,l as O,m as tr,n as ar,o as nr,p as or,q as ir,r as lr,s as cr,t as ur,v as dr,P as me,w as fr,x as be,y as hr,z as pr,E as xr,H as gr,J as mr}from"./mui-519b3a10.js";import{c as br,g as jr,r as c,a as vr}from"./vendor-e1e3bd03.js";import{m as je,p as yr}from"./utils-3ec4b360.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))u(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const a of l.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&u(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function u(i){if(i.ep)return;i.ep=!0;const l=n(i);fetch(i.href,l)}})();var K={},ve=br;K.createRoot=ve.createRoot,K.hydrateRoot=ve.hydrateRoot;var Z={},G={};const wr=jr(Xe);var ye;function I(){return ye||(ye=1,function(r){"use client";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.createSvgIcon}});var s=wr}(G)),G}var _r=R;Object.defineProperty(Z,"__esModule",{value:!0});var Ee=Z.default=void 0,Sr=_r(I()),Cr=e;Ee=Z.default=(0,Sr.default)((0,Cr.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search");var ee={},kr=R;Object.defineProperty(ee,"__esModule",{value:!0});var Re=ee.default=void 0,$r=kr(I()),Er=e;Re=ee.default=(0,$r.default)((0,Er.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");var re={},Rr=R;Object.defineProperty(re,"__esModule",{value:!0});var W=re.default=void 0,Ir=Rr(I()),zr=e;W=re.default=(0,Ir.default)((0,zr.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore");var se={},Tr=R;Object.defineProperty(se,"__esModule",{value:!0});var X=se.default=void 0,Dr=Tr(I()),Mr=e;X=se.default=(0,Dr.default)((0,Mr.jsx)("path",{d:"m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"}),"ExpandLess");const E="/api/v1";class Pr{async searchCVEs(s,n=1,u=20){const i=new URLSearchParams({query:s,page:n.toString(),page_size:u.toString()}),l=await fetch(`${E}/search?${i}`);if(!l.ok)throw new Error(`搜索失败: ${l.statusText}`);return l.json()}async getCVEDetail(s){const n=await fetch(`${E}/cve/${s}`);if(!n.ok)throw new Error(`获取 CVE 详情失败: ${n.statusText}`);return n.json()}async getCVESummary(s,n=!1){const u=new URLSearchParams;n&&u.append("force_refresh","true");const i=await fetch(`${E}/summary/${s}?${u}`);if(!i.ok)throw new Error(`获取总结失败: ${i.statusText}`);const l=await i.json();return console.log(l),l.summary&&typeof l.summary!="string"&&(l.summary=String(l.summary)),l}async getCVESummaryStream(s,n=!1){const u=new URLSearchParams;n&&u.append("force_refresh","true");const i=await fetch(`${E}/summary/${s}/stream?${u}`);if(!i.ok)throw new Error(`流式获取总结失败: ${i.statusText}`);return i.body}async getCVERawData(s){const n=await fetch(`${E}/cve/${s}/raw`);if(!n.ok)throw new Error(`获取原始数据失败: ${n.statusText}`);return n.json()}async getSearchExamples(){const s=await fetch(`${E}/examples`);if(!s.ok)throw new Error(`获取搜索示例失败: ${s.statusText}`);return s.json()}async healthCheck(){const s=await fetch(`${E}/health`);if(!s.ok)throw new Error(`健康检查失败: ${s.statusText}`);return s.json()}}const J=new Pr,Lr=()=>{const[r,s]=c.useState(null),[n,u]=c.useState(!1),[i,l]=c.useState(!1),a=c.useCallback(d=>{const g=[];if(d.vulnerability.length>0){const o=[];d.vulnerability.forEach(p=>{switch(p){case"buffer_overflow":o.push('"buffer overflow"','"buffer-overflow"','"buffer_overflow"');break;case"sql_injection":o.push("+sql injection, +sql_injection, +SQLi");break;case"code_execution":o.push('"remote code execution"','"remote-code-execution"','" RCE "','"(RCE)"');break;case"file_access":o.push("+path traversal, +path_traversal, +LFI, +RFI");break;case"xss":o.push("+cross site scripting, +cross-site-scripting, +XSS");break;case"csrf":o.push("+cross site request forgery, +cross-site-request-forgery, +CSRF");break;case"privilege_escalation":o.push("+privilege escalation, +privilege_escalation, +elevation");break;case"information_disclosure":o.push("+information disclosure, +information_disclosure, +data leak");break}}),o.length>0&&g.push(`(${o.join(" | ")})`)}if(d.software.length>0){const o=[];d.software.forEach(p=>{switch(p){case"server":o.push("+apache, +nginx, +tomcat, +iis");break;case"database":o.push("+mysql, +mariadb, +postgresql, +mongodb");break;case"os":o.push("+windows, +linux, +macos, +unix");break;case"browser":o.push("+chrome, +firefox, +safari, +edge, +webkit");break}}),o.length>0&&g.push(`(${o.join(" | ")})`)}if(d.language.length>0){const o=[];d.language.forEach(p=>{switch(p){case"php":o.push("+php");break;case"java":o.push("+java");break;case"python":o.push("+python");break;case"javascript":o.push("+javascript, +nodejs, +node.js");break;case"c_cpp":o.push('+c++, +cpp, +"c programming"');break;case"dotnet":o.push("+.net, +dotnet, +asp.net");break}}),o.length>0&&g.push(`(${o.join(" | ")})`)}if(d.severity.length>0){const o=[];d.severity.forEach(p=>{switch(p){case"critical":o.push("+critical");break;case"high":o.push("+high");break;case"medium":o.push("+medium");break;case"low":o.push("+low");break}}),o.length>0&&g.push(`(${o.join(" | ")})`)}return g.join(", ")},[]),m=c.useCallback(async(d,g,o,p,w=!0)=>{const v=d.trim(),_=a(g);if(!v&&!_)throw new Error("请输入搜索条件或选择筛选条件");u(!0),l(!0);try{let y="";v&&_?y=`${v}, ${_}`:v?y=v:y=_;const S=await J.searchCVEs(y,w?1:o,p);return s(S),S}finally{u(!1)}},[a]);return{searchResults:r,searching:n,hasSearched:i,setSearchResults:s,performSearch:m}},Ar=({showExamples:r,onExampleClick:s})=>{const[n,u]=c.useState([]);c.useEffect(()=>{(async()=>{try{const a=await J.getSearchExamples();u(a.examples)}catch(a){console.error("加载示例失败:",a)}})()},[]);const i=l=>{s(l)};return e.jsx(Y,{in:r,children:e.jsxs(f,{sx:{mt:2,p:3,backgroundColor:"rgba(255, 255, 255, 0.95)",borderRadius:2},children:[e.jsx(h,{variant:"h6",sx:{mb:2,color:"#409eff"},children:"🎯 搜索语法示例"}),n.map((l,a)=>e.jsx(Se,{sx:{mb:2,cursor:"pointer",transition:"all 0.3s","&:hover":{transform:"translateX(5px)",backgroundColor:"#e3f2fd"}},onClick:()=>i(l.query),children:e.jsxs(Ce,{children:[e.jsx(h,{variant:"subtitle1",sx:{color:"#409eff",fontWeight:"bold"},children:l.title}),e.jsx(h,{variant:"body2",sx:{fontFamily:"monospace",backgroundColor:"#fff",p:1,border:"1px solid #ddd",borderRadius:1,my:1},children:l.query}),e.jsx(h,{variant:"body2",color:"text.secondary",children:l.description})]})},a))]})})},qr={vulnerability:[{label:"缓冲区溢出",value:"buffer_overflow",description:"包含堆栈或堆缓冲区溢出漏洞"},{label:"任意代码执行",value:"code_execution",description:"包含远程代码执行漏洞"},{label:"任意文件读取",value:"file_access",description:"包含路径遍历和文件包含漏洞"},{label:"SQL 注入",value:"sql_injection",description:"包含 SQL 注入相关漏洞"},{label:"跨站脚本(XSS)",value:"xss",description:"包含跨站脚本攻击漏洞"},{label:"跨站请求伪造(CSRF)",value:"csrf",description:"包含跨站请求伪造漏洞"},{label:"权限提升",value:"privilege_escalation",description:"包含权限提升漏洞"},{label:"信息泄露",value:"information_disclosure",description:"包含信息泄露漏洞"}],software:[{label:"服务器软件",value:"server",description:"包含 Apache、Nginx、Tomcat、IIS 等服务器软件"},{label:"数据库",value:"database",description:"包含 MySQL、PostgreSQL、MongoDB 等数据库"},{label:"操作系统",value:"os",description:"包含 Windows、Linux、macOS 等操作系统"},{label:"浏览器",value:"browser",description:"包含 Chrome、Firefox、Safari、Edge 等浏览器"}],language:[{label:"PHP",value:"php",description:"包含 PHP 相关漏洞"},{label:"Java",value:"java",description:"包含 Java 相关漏洞"},{label:"Python",value:"python",description:"包含 Python 相关漏洞"},{label:"JavaScript",value:"javascript",description:"包含 JavaScript 相关漏洞"},{label:"C/C++",value:"c_cpp",description:"包含 C/C++ 相关漏洞"},{label:".NET",value:"dotnet",description:"包含 .NET 相关漏洞"}],severity:[{label:"严重 (Critical)",value:"critical",description:"严重程度为 Critical 的漏洞"},{label:"高危 (High)",value:"high",description:"严重程度为 High 的漏洞"},{label:"中危 (Medium)",value:"medium",description:"严重程度为 Medium 的漏洞"},{label:"低危 (Low)",value:"low",description:"严重程度为 Low 的漏洞"}]},Or={vulnerability:"漏洞类型",software:"软件类型",language:"开发语言",severity:"严重程度"},Wr={vulnerability:"warning",software:"settings",language:"code",severity:"security"},Hr=({showFilters:r,selectedFilters:s,onFilterChange:n})=>{const u=Object.values(s).reduce((a,m)=>a+m.length,0),i=(a,m,d)=>{const g={...s,[a]:d?[...s[a],m]:s[a].filter(o=>o!==m)};n(g)},l=()=>{n({vulnerability:[],software:[],language:[],severity:[]})};return e.jsx(Y,{in:r,children:e.jsxs(f,{sx:{mt:2,p:3,backgroundColor:"rgba(255, 255, 255, 0.95)",borderRadius:2},children:[e.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(h,{variant:"h6",sx:{color:"#409eff",display:"flex",alignItems:"center",gap:1},children:"🎯 快速筛选"}),u>0&&e.jsx($,{size:"small",color:"error",onClick:l,children:"清空筛选"})]}),e.jsx(q,{container:!0,spacing:2,children:Object.entries(qr).map(([a,m])=>e.jsx(q,{item:!0,xs:12,md:6,children:e.jsxs(Ye,{defaultExpanded:!0,children:[e.jsx(Ze,{expandIcon:e.jsx(W,{}),children:e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx("span",{className:"material-icons",children:Wr[a]}),e.jsx(h,{children:Or[a]}),s[a].length>0&&e.jsx(ke,{badgeContent:s[a].length,color:"primary",sx:{ml:1}})]})}),e.jsx(er,{children:e.jsx(rr,{children:m.map(d=>e.jsx($e,{control:e.jsx(sr,{checked:s[a].includes(d.value),onChange:g=>i(a,d.value,g.target.checked)}),label:d.label,title:d.description},d.value))})})]})},a))})]})})};var te={},Br=R;Object.defineProperty(te,"__esModule",{value:!0});var Ie=te.default=void 0,Vr=Br(I()),Fr=e;Ie=te.default=(0,Vr.default)((0,Fr.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}),"Refresh");var ae={},Jr=R;Object.defineProperty(ae,"__esModule",{value:!0});var ze=ae.default=void 0,Qr=Jr(I()),Nr=e;ze=ae.default=(0,Qr.default)((0,Nr.jsx)("path",{d:"M9.4 16.6 4.8 12l4.6-4.6L8 6l-6 6 6 6zm5.2 0 4.6-4.6-4.6-4.6L16 6l6 6-6 6z"}),"Code");const Gr=r=>{if(!r)return"";try{je.setOptions({breaks:!0,gfm:!0});const s=je.parse(r);return yr.sanitize(s)}catch(s){return console.error("Markdown 渲染错误:",s),r.replace(/\n/g,"<br>")}},we=r=>{if(!r)return"未知";try{return new Date(r).toLocaleDateString("zh-CN")}catch{return r}},Ur=(r,s=500)=>r?r.length<=s?r:r.substring(0,s)+"...":"",_e=r=>({CRITICAL:"error",HIGH:"error",MEDIUM:"warning",LOW:"info"})[(r==null?void 0:r.toUpperCase())||""]||"default",Kr=r=>r?r.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,function(s){let n="json-number";return/^"/.test(s)?/:$/.test(s)?n="json-key":n="json-string":/true|false/.test(s)?n="json-boolean":/null/.test(s)&&(n="json-null"),'<span class="'+n+'">'+s+"</span>"}).replace(/([{}[\],])/g,'<span class="json-punctuation">$1</span>'):"",Xr=r=>{if(!r)return"";try{return JSON.stringify(r,null,2)}catch(s){return console.error("JSON 格式化错误:",s),JSON.stringify(r)}},Yr=({text:r,speed:s=20,instanceId:n})=>{const[u,i]=c.useState(""),l=c.useRef(""),a=c.useRef(0),m=c.useRef(null),d=c.useRef(!1),g=c.useRef(""),o=c.useRef(n);c.useEffect(()=>{o.current=n},[n]);const p=c.useCallback(()=>{m.current&&(clearTimeout(m.current),m.current=null)},[]),w=c.useCallback(()=>{o.current===n&&(a.current<l.current.length?(i(l.current.substring(0,a.current+1)),a.current++,m.current=window.setTimeout(w,s)):d.current=!1)},[s,n]);return c.useEffect(()=>{if(r!==g.current){if(g.current=r,l.current=r,!r){p(),i(""),a.current=0,d.current=!1;return}r.length<u.length&&(p(),i(""),a.current=0,d.current=!1),r.length>u.length?(p(),d.current||(d.current=!0,a.current=u.length,w())):r.length===u.length&&r!==u&&(p(),i(""),a.current=0,d.current=!0,w())}},[r,u.length,w,p]),c.useEffect(()=>()=>{p()},[p]),u},Zr=({text:r,speed:s=20,instanceId:n})=>{const u=c.useMemo(()=>n||`typewriter-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,[n]),i=Yr({text:r,speed:s,instanceId:u});return e.jsx(f,{sx:{lineHeight:1.6},dangerouslySetInnerHTML:{__html:Gr(i)}})},es=({cve:r,loadingSummary:s,streamingSummary:n,onGenerateSummary:u,onRefreshSummary:i,onViewRawData:l})=>{var _;const[a,m]=c.useState(!1),[d,g]=c.useState(Date.now()),o=()=>{m(!a)},p=()=>{g(Date.now()),u(r.cve_info.cve_id)},w=()=>{g(Date.now()),i(r.cve_info.cve_id)},v=()=>{l(r.cve_info.cve_id)};return e.jsx(Se,{sx:{mb:3,borderRadius:3,overflow:"hidden"},children:e.jsxs(Ce,{children:[e.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(f,{sx:{flex:1},children:[e.jsx(h,{variant:"h5",sx:{color:"#e74c3c",fontWeight:"bold",mb:1},children:r.cve_info.cve_id}),e.jsxs(F,{direction:"row",spacing:1,flexWrap:"wrap",sx:{mb:1},children:[e.jsx(k,{label:`发布: ${we(r.cve_info.published_date)}`,size:"small",color:"info"}),e.jsx(k,{label:`修改: ${we(r.cve_info.last_modified_date)}`,size:"small",color:"default"}),r.cve_info.cvss_score&&e.jsx(k,{label:`CVSS: ${r.cve_info.cvss_score}`,size:"small",color:_e(r.cve_info.cvss_severity)}),r.cve_info.cvss_severity&&e.jsx(k,{label:r.cve_info.cvss_severity,size:"small",color:_e(r.cve_info.cvss_severity)})]})]}),e.jsxs(f,{sx:{display:"flex",gap:1},children:[e.jsx(U,{onClick:v,size:"small",title:"查看原始 JSON 数据",children:e.jsx(ze,{})}),e.jsx(U,{onClick:o,size:"small",sx:{transform:a?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"},children:e.jsx(W,{})})]})]}),r.cve_info.title&&e.jsx(h,{variant:"subtitle1",sx:{mb:1,fontWeight:"bold",color:"text.secondary"},children:r.cve_info.title}),e.jsx(h,{variant:"body1",sx:{mb:2,lineHeight:1.6},children:Ur(r.cve_info.description,300)}),e.jsx(Y,{in:a,children:e.jsxs(f,{sx:{mt:2},children:[e.jsx(ge,{sx:{mb:2}}),e.jsx(h,{variant:"body1",sx:{mb:2,lineHeight:1.6},children:r.cve_info.description}),(r.cve_info.cwe_ids||[]).length>0&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(h,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"CWE 类型:"}),e.jsx(F,{direction:"row",spacing:1,flexWrap:"wrap",children:(r.cve_info.cwe_ids||[]).map((y,S)=>e.jsx(k,{label:y,size:"small",variant:"outlined"},S))})]}),(r.cve_info.affected_products||[]).length>0&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(h,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"受影响的产品:"}),e.jsxs(F,{direction:"row",spacing:1,flexWrap:"wrap",children:[(r.cve_info.affected_products||[]).slice(0,10).map((y,S)=>e.jsx(k,{label:y,size:"small",color:"warning"},S)),(r.cve_info.affected_products||[]).length>10&&e.jsx(k,{label:`+${r.cve_info.affected_products.length-10} 更多`,size:"small",variant:"outlined"})]})]}),(r.cve_info.references||[]).length>0&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(h,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"参考链接:"}),e.jsxs(f,{sx:{maxHeight:100,overflow:"auto"},children:[(r.cve_info.references||[]).slice(0,5).map((y,S)=>e.jsx(h,{variant:"body2",sx:{mb:.5},children:e.jsx("a",{href:y,target:"_blank",rel:"noopener noreferrer",style:{color:"#409eff"},children:y})},S)),(r.cve_info.references||[]).length>5&&e.jsxs(h,{variant:"body2",color:"text.secondary",children:["... 还有 ",r.cve_info.references.length-5," 个链接"]})]})]})]})}),e.jsx(ge,{sx:{my:2}}),e.jsxs(f,{children:[e.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(h,{variant:"h6",sx:{color:"#409eff",display:"flex",alignItems:"center",gap:1},children:"🤖 AI 总结"}),e.jsxs(f,{sx:{display:"flex",gap:1},children:[r.summary&&e.jsx($,{size:"small",startIcon:e.jsx(Ie,{}),onClick:w,disabled:s,children:"刷新"}),!r.summary&&!n&&e.jsx($,{size:"small",variant:"contained",onClick:p,disabled:s,startIcon:s?e.jsx(O,{size:16}):null,children:"生成总结"})]})]}),s&&!n&&e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:2,p:2},children:[e.jsx(O,{size:20}),e.jsx(h,{variant:"body2",color:"text.secondary",children:"正在生成 AI 总结..."})]}),(n||r.summary&&!n)&&e.jsx(f,{sx:{p:2,backgroundColor:"#f8f9fa",borderRadius:2,border:"1px solid #e9ecef"},children:e.jsx(Zr,{text:n||((_=r.summary)==null?void 0:_.summary)||"",speed:20,instanceId:`${r.cve_info.cve_id}-${d}-${n?"stream":"cache"}`},`${r.cve_info.cve_id}-${d}-${n?"stream":"cache"}`)}),!r.summary&&!n&&!s&&e.jsx(h,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:'点击"生成总结"按钮获取 AI 分析'})]})]})})};var ne={},rs=R;Object.defineProperty(ne,"__esModule",{value:!0});var Te=ne.default=void 0,ss=rs(I()),ts=e;Te=ne.default=(0,ss.default)((0,ts.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");const as=({jsonModal:r,onClose:s,onToggleWordWrap:n})=>{const u=r.data?Xr(r.data):"",i=Kr(u);return e.jsxs(tr,{open:r.open,onClose:s,maxWidth:"lg",fullWidth:!0,PaperProps:{sx:{height:"80vh",backgroundColor:"#1e1e1e",color:"white"}},children:[e.jsxs(ar,{sx:{backgroundColor:"#2d2d2d",color:"#61dafb",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #404040"},children:[e.jsxs(h,{variant:"h6",children:["CVE 原始数据: ",r.cveId]}),e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx($e,{control:e.jsx(nr,{checked:r.wordWrap,onChange:n,size:"small",sx:{"& .MuiSwitch-switchBase.Mui-checked":{color:"#4caf50"},"& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":{backgroundColor:"#4caf50"}}}),label:e.jsx(h,{variant:"body2",sx:{color:"#ccc"},children:"自动换行"})}),e.jsx(U,{onClick:s,size:"small",sx:{color:"#ccc","&:hover":{backgroundColor:"#404040",color:"white"}},children:e.jsx(Te,{})})]})]}),e.jsx(or,{sx:{p:0,backgroundColor:"#1e1e1e",overflow:"auto"},children:r.loading?e.jsxs(f,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:2},children:[e.jsx(O,{sx:{color:"#61dafb"}}),e.jsx(h,{sx:{color:"#ccc"},children:"正在加载原始数据..."})]}):e.jsx(f,{sx:{fontFamily:'"Courier New", "Monaco", "Menlo", monospace',fontSize:"14px",lineHeight:1.4,p:2,whiteSpace:r.wordWrap?"pre-wrap":"pre",wordBreak:r.wordWrap?"break-word":"normal",overflow:"auto"},dangerouslySetInnerHTML:{__html:i}})}),e.jsx(ir,{sx:{backgroundColor:"#2d2d2d",borderTop:"1px solid #404040"},children:e.jsx($,{onClick:s,sx:{color:"#ccc","&:hover":{backgroundColor:"#404040"}},children:"关闭"})})]})},ns=lr({palette:{primary:{main:"#409eff"},secondary:{main:"#67c23a"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif'}}),os=()=>{const[r,s]=c.useState(""),[n,u]=c.useState(20),[i,l]=c.useState(1),{searchResults:a,searching:m,hasSearched:d,setSearchResults:g,performSearch:o}=Lr(),[p,w]=c.useState(!0),[v,_]=c.useState(!1),[y,S]=c.useState(!1),[z,De]=c.useState({vulnerability:[],software:[],language:[],severity:[]}),[Me,oe]=c.useState({}),[Pe,H]=c.useState({}),[B,ie]=c.useState({}),[Le,Ae]=c.useState({}),[Q,le]=c.useState({open:!1,message:"",severity:"info"}),[qe,L]=c.useState({open:!1,cveId:"",data:null,loading:!1,wordWrap:!0}),Oe=[10,20,50,100],T=(t,x="info")=>{le({open:!0,message:t,severity:x})},ce=()=>{le(t=>({...t,open:!1}))},ue=c.useCallback(async(t=!0)=>{try{const x=await o(r,z,i,n,t);t&&(l(1),w(!1),_(!1),T(`找到 ${x.total_results} 个结果`,"success"))}catch(x){T(x.message,"error")}},[r,z,i,n,o]),We=(t,x)=>{l(x),d&&o(r,z,x,n,!1)},He=t=>{u(t),l(1),d&&r.trim()&&o(r,z,1,t,!1)},Be=t=>{s(t),w(!1)},Ve=t=>{De(t)},de=async t=>{B[t]&&B[t].abort();const x=new AbortController,D=`${t}-${Date.now()}`;ie(b=>({...b,[t]:x})),Ae(b=>({...b,[t]:D})),oe(b=>({...b,[t]:!0})),H(b=>({...b,[t]:""}));try{const b=await J.getCVESummaryStream(t);if(!b)throw new Error("无法获取流式响应");const M=b.getReader(),V=new TextDecoder;let N="",he=!1;for(;!x.signal.aborted;){const{done:Ue,value:Ke}=await M.read();if(Ue)break;N+=V.decode(Ke,{stream:!0});const pe=N.split(`
`);N=pe.pop()||"";for(const xe of pe)if(xe.startsWith("data: "))try{const C=JSON.parse(xe.slice(6));if(Le[t]!==D){console.log(`请求已过期，忽略数据: ${t}`);return}if(C.type==="cache"){he=!0;const P=C.data;if(a&&a.results){const A=a.results.map(j=>j.cve_info.cve_id===t?{...j,summary:P}:j);g(j=>j?{...j,results:A}:null)}T("从缓存获取 AI 总结","info");break}else if(C.type==="chunk")H(P=>({...P,[t]:(P[t]||"")+C.data}));else if(C.type==="complete"){const P=C.data;if(a&&a.results){const A=a.results.map(j=>j.cve_info.cve_id===t?{...j,summary:P}:j);g(j=>j?{...j,results:A}:null)}H(A=>{const j={...A};return delete j[t],j}),T("AI 总结生成成功","success")}else if(C.type==="error")throw new Error(C.data)}catch(C){console.error("解析流式数据错误:",C)}if(he)break}}catch(b){if(x.signal.aborted){console.log(`请求被取消: ${t}`);return}console.error("生成总结错误:",b),T(`生成总结失败: ${b.message}`,"error"),H(M=>{const V={...M};return delete V[t],V})}finally{ie(b=>{const M={...b};return delete M[t],M}),oe(b=>({...b,[t]:!1}))}},Fe=async t=>{await de(t)},Je=async t=>{L(x=>({...x,open:!0,cveId:t,loading:!0,data:null}));try{const x=await J.getCVERawData(t);L(D=>({...D,data:x.raw_data,loading:!1}))}catch(x){console.error("获取原始数据错误:",x),T(`获取原始数据失败: ${x.message}`,"error"),L(D=>({...D,open:!1,loading:!1}))}},Qe=()=>{L({open:!1,cveId:"",data:null,loading:!1,wordWrap:!0})},Ne=()=>{L(t=>({...t,wordWrap:!t.wordWrap}))};c.useEffect(()=>{const t=()=>{S(window.scrollY>300)};return window.addEventListener("scroll",t),()=>window.removeEventListener("scroll",t)},[]),c.useEffect(()=>()=>{Object.values(B).forEach(t=>{t.abort()})},[B]);const Ge=()=>{window.scrollTo({top:0,behavior:"smooth"})},fe=Object.values(z).reduce((t,x)=>t+x.length,0);return e.jsxs(cr,{theme:ns,children:[e.jsx(ur,{}),e.jsxs("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",padding:"20px"},children:[e.jsxs(f,{sx:{textAlign:"center",color:"white",mb:5},children:[e.jsx(h,{variant:"h2",component:"h1",sx:{textShadow:"2px 2px 4px rgba(0,0,0,0.3)"},children:"🔍 CVE 搜索服务"}),e.jsx(h,{variant:"h6",sx:{opacity:.9,mt:1},children:"基于 AI 的漏洞信息搜索与总结平台"})]}),e.jsx(dr,{maxWidth:"lg",children:e.jsxs(me,{elevation:3,sx:{borderRadius:"20px",overflow:"hidden"},children:[e.jsxs(f,{sx:{p:4,background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},children:[e.jsx(fr,{fullWidth:!0,variant:"outlined",placeholder:"输入搜索条件，例如：remote code execution",value:r,onChange:t=>s(t.target.value),onKeyDown:t=>t.key==="Enter"&&ue(),InputProps:{startAdornment:e.jsx(be,{position:"start",children:e.jsx(Ee,{})}),endAdornment:e.jsx(be,{position:"end",children:e.jsx($,{variant:"contained",onClick:()=>ue(),disabled:m,startIcon:m?e.jsx(O,{size:20}):null,children:"搜索"})})},sx:{mb:2}}),e.jsx(me,{sx:{p:2,backgroundColor:"rgba(255, 255, 255, 0.8)"},children:e.jsxs(f,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2},children:[e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(h,{variant:"body2",children:"每页显示："}),e.jsx(hr,{size:"small",children:Oe.map(t=>e.jsx($,{variant:n===t?"contained":"outlined",onClick:()=>He(t),children:t},t))})]}),e.jsxs(f,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsxs($,{size:"small",variant:v?"contained":"outlined",onClick:()=>_(!v),startIcon:v?e.jsx(X,{}):e.jsx(W,{}),endIcon:fe>0?e.jsx(ke,{badgeContent:fe,color:"primary"}):null,children:[v?"隐藏":"显示","筛选器"]}),e.jsxs($,{size:"small",variant:p?"contained":"outlined",onClick:()=>w(!p),startIcon:p?e.jsx(X,{}):e.jsx(W,{}),children:[p?"隐藏":"显示","搜索示例"]})]})]})}),e.jsx(Hr,{showFilters:v,selectedFilters:z,onFilterChange:Ve}),e.jsx(Ar,{showExamples:p,onExampleClick:Be})]}),d&&e.jsx(f,{sx:{p:4},children:m?e.jsxs(f,{sx:{textAlign:"center",p:4},children:[e.jsx(O,{size:40}),e.jsx(h,{sx:{mt:2},children:"正在搜索中..."})]}):a?e.jsxs(e.Fragment,{children:[e.jsx(f,{sx:{p:2,backgroundColor:"rgba(255, 255, 255, 0.9)",borderRadius:2,mb:2},children:e.jsxs(q,{container:!0,justifyContent:"space-between",alignItems:"center",children:[e.jsx(q,{item:!0,xs:12,md:8,children:e.jsxs(F,{direction:"row",spacing:1,flexWrap:"wrap",children:[e.jsx(k,{icon:e.jsx("span",{className:"material-icons",children:"check_circle"}),label:`共找到 ${a.total_results} 个 CVE`,color:"success",size:"medium"}),e.jsx(k,{label:`第 ${a.page} / ${a.total_pages} 页`,color:"info"}),e.jsx(k,{label:`显示 ${(a.page-1)*a.page_size+1}-${Math.min(a.page*a.page_size,a.total_results)}`,color:"warning"})]})}),e.jsx(q,{item:!0,xs:12,md:4,sx:{textAlign:{xs:"left",md:"right"},mt:{xs:1,md:0}},children:e.jsxs(h,{variant:"body2",color:"text.secondary",children:["搜索耗时: ",a.search_time.toFixed(3),"s"]})})]})}),a.results.map(t=>e.jsx(es,{cve:t,loadingSummary:Me[t.cve_info.cve_id]||!1,streamingSummary:Pe[t.cve_info.cve_id],onGenerateSummary:de,onRefreshSummary:Fe,onViewRawData:Je},t.cve_info.cve_id)),a.total_pages>1&&e.jsx(f,{sx:{display:"flex",justifyContent:"center",mt:4},children:e.jsx(pr,{count:a.total_pages,page:i,onChange:We,color:"primary",size:"large",showFirstButton:!0,showLastButton:!0})})]}):null})]})}),y&&e.jsx(xr,{color:"primary",size:"medium",onClick:Ge,sx:{position:"fixed",bottom:24,right:24,zIndex:1e3},children:e.jsx(Re,{})}),e.jsx(as,{jsonModal:qe,onClose:Qe,onToggleWordWrap:Ne}),e.jsx(gr,{open:Q.open,autoHideDuration:6e3,onClose:ce,anchorOrigin:{vertical:"top",horizontal:"center"},children:e.jsx(mr,{onClose:ce,severity:Q.severity,sx:{width:"100%"},children:Q.message})})]})]})};K.createRoot(document.getElementById("root")).render(e.jsx(vr.StrictMode,{children:e.jsx(os,{})}));
