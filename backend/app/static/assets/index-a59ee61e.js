import{u as Je,i as R,j as e,C as U,B as c,T as f,a as ve,b as ye,c as C,G as L,A as Qe,d as Ne,e as we,f as Ge,F as Ue,g as _e,h as Ke,S as H,k as S,I as Q,D as fe,l as A,m as Xe,n as Ye,o as Ze,p as er,q as rr,r as sr,s as tr,t as ar,v as nr,P as he,w as or,x as pe,y as ir,z as lr,E as cr,H as dr,J as ur}from"./mui-519b3a10.js";import{c as fr,g as hr,r as u,a as pr}from"./vendor-e1e3bd03.js";import{m as xe,p as xr}from"./utils-3ec4b360.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))h(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const t of o.addedNodes)t.tagName==="LINK"&&t.rel==="modulepreload"&&h(t)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function h(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();var N={},me=fr;N.createRoot=me.createRoot,N.hydrateRoot=me.hydrateRoot;var K={},J={};const mr=hr(Je);var ge;function I(){return ge||(ge=1,function(r){"use client";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.createSvgIcon}});var s=mr}(J)),J}var gr=R;Object.defineProperty(K,"__esModule",{value:!0});var Se=K.default=void 0,br=gr(I()),jr=e;Se=K.default=(0,br.default)((0,jr.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search");var X={},vr=R;Object.defineProperty(X,"__esModule",{value:!0});var Ce=X.default=void 0,yr=vr(I()),wr=e;Ce=X.default=(0,yr.default)((0,wr.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");var Y={},_r=R;Object.defineProperty(Y,"__esModule",{value:!0});var W=Y.default=void 0,Sr=_r(I()),Cr=e;W=Y.default=(0,Sr.default)((0,Cr.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore");var Z={},kr=R;Object.defineProperty(Z,"__esModule",{value:!0});var G=Z.default=void 0,Er=kr(I()),$r=e;G=Z.default=(0,Er.default)((0,$r.jsx)("path",{d:"m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"}),"ExpandLess");const $="/api/v1";class Rr{async searchCVEs(s,n=1,h=20){const l=new URLSearchParams({query:s,page:n.toString(),page_size:h.toString()}),o=await fetch(`${$}/search?${l}`);if(!o.ok)throw new Error(`搜索失败: ${o.statusText}`);return o.json()}async getCVEDetail(s){const n=await fetch(`${$}/cve/${s}`);if(!n.ok)throw new Error(`获取 CVE 详情失败: ${n.statusText}`);return n.json()}async getCVESummary(s,n=!1){const h=new URLSearchParams;n&&h.append("force_refresh","true");const l=await fetch(`${$}/summary/${s}?${h}`);if(!l.ok)throw new Error(`获取总结失败: ${l.statusText}`);const o=await l.json();return console.log(o),o.summary&&typeof o.summary!="string"&&(o.summary=String(o.summary)),o}async getCVESummaryStream(s,n=!1){const h=new URLSearchParams;n&&h.append("force_refresh","true");const l=await fetch(`${$}/summary/${s}/stream?${h}`);if(!l.ok)throw new Error(`流式获取总结失败: ${l.statusText}`);return l.body}async getCVERawData(s){const n=await fetch(`${$}/cve/${s}/raw`);if(!n.ok)throw new Error(`获取原始数据失败: ${n.statusText}`);return n.json()}async getSearchExamples(){const s=await fetch(`${$}/examples`);if(!s.ok)throw new Error(`获取搜索示例失败: ${s.statusText}`);return s.json()}async healthCheck(){const s=await fetch(`${$}/health`);if(!s.ok)throw new Error(`健康检查失败: ${s.statusText}`);return s.json()}}const B=new Rr,Ir=()=>{const[r,s]=u.useState(null),[n,h]=u.useState(!1),[l,o]=u.useState(!1),t=u.useCallback(p=>{const m=[];if(p.vulnerability.length>0){const i=[];p.vulnerability.forEach(g=>{switch(g){case"buffer_overflow":i.push('"buffer overflow"','"buffer-overflow"','"buffer_overflow"');break;case"sql_injection":i.push("+sql injection, +sql_injection, +SQLi");break;case"code_execution":i.push('"remote code execution"','"remote-code-execution"','" RCE "','"(RCE)"');break;case"file_access":i.push("+path traversal, +path_traversal, +LFI, +RFI");break;case"xss":i.push("+cross site scripting, +cross-site-scripting, +XSS");break;case"csrf":i.push("+cross site request forgery, +cross-site-request-forgery, +CSRF");break;case"privilege_escalation":i.push("+privilege escalation, +privilege_escalation, +elevation");break;case"information_disclosure":i.push("+information disclosure, +information_disclosure, +data leak");break}}),i.length>0&&m.push(`(${i.join(" | ")})`)}if(p.software.length>0){const i=[];p.software.forEach(g=>{switch(g){case"server":i.push("+apache, +nginx, +tomcat, +iis");break;case"database":i.push("+mysql, +mariadb, +postgresql, +mongodb");break;case"os":i.push("+windows, +linux, +macos, +unix");break;case"browser":i.push("+chrome, +firefox, +safari, +edge, +webkit");break}}),i.length>0&&m.push(`(${i.join(" | ")})`)}if(p.language.length>0){const i=[];p.language.forEach(g=>{switch(g){case"php":i.push("+php");break;case"java":i.push("+java");break;case"python":i.push("+python");break;case"javascript":i.push("+javascript, +nodejs, +node.js");break;case"c_cpp":i.push('+c++, +cpp, +"c programming"');break;case"dotnet":i.push("+.net, +dotnet, +asp.net");break}}),i.length>0&&m.push(`(${i.join(" | ")})`)}if(p.severity.length>0){const i=[];p.severity.forEach(g=>{switch(g){case"critical":i.push("+critical");break;case"high":i.push("+high");break;case"medium":i.push("+medium");break;case"low":i.push("+low");break}}),i.length>0&&m.push(`(${i.join(" | ")})`)}return m.join(", ")},[]),x=u.useCallback(async(p,m,i,g,k=!0)=>{const j=p.trim(),y=t(m);if(!j&&!y)throw new Error("请输入搜索条件或选择筛选条件");h(!0),o(!0);try{let v="";j&&y?v=`${j}, ${y}`:j?v=j:v=y;const w=await B.searchCVEs(v,k?1:i,g);return s(w),w}finally{h(!1)}},[t]);return{searchResults:r,searching:n,hasSearched:l,setSearchResults:s,performSearch:x}},zr=({showExamples:r,onExampleClick:s})=>{const[n,h]=u.useState([]);u.useEffect(()=>{(async()=>{try{const t=await B.getSearchExamples();h(t.examples)}catch(t){console.error("加载示例失败:",t)}})()},[]);const l=o=>{s(o)};return e.jsx(U,{in:r,children:e.jsxs(c,{sx:{mt:2,p:3,backgroundColor:"rgba(255, 255, 255, 0.95)",borderRadius:2},children:[e.jsx(f,{variant:"h6",sx:{mb:2,color:"#409eff"},children:"🎯 搜索语法示例"}),n.map((o,t)=>e.jsx(ve,{sx:{mb:2,cursor:"pointer",transition:"all 0.3s","&:hover":{transform:"translateX(5px)",backgroundColor:"#e3f2fd"}},onClick:()=>l(o.query),children:e.jsxs(ye,{children:[e.jsx(f,{variant:"subtitle1",sx:{color:"#409eff",fontWeight:"bold"},children:o.title}),e.jsx(f,{variant:"body2",sx:{fontFamily:"monospace",backgroundColor:"#fff",p:1,border:"1px solid #ddd",borderRadius:1,my:1},children:o.query}),e.jsx(f,{variant:"body2",color:"text.secondary",children:o.description})]})},t))]})})},Tr={vulnerability:[{label:"缓冲区溢出",value:"buffer_overflow",description:"包含堆栈或堆缓冲区溢出漏洞"},{label:"任意代码执行",value:"code_execution",description:"包含远程代码执行漏洞"},{label:"任意文件读取",value:"file_access",description:"包含路径遍历和文件包含漏洞"},{label:"SQL 注入",value:"sql_injection",description:"包含 SQL 注入相关漏洞"},{label:"跨站脚本(XSS)",value:"xss",description:"包含跨站脚本攻击漏洞"},{label:"跨站请求伪造(CSRF)",value:"csrf",description:"包含跨站请求伪造漏洞"},{label:"权限提升",value:"privilege_escalation",description:"包含权限提升漏洞"},{label:"信息泄露",value:"information_disclosure",description:"包含信息泄露漏洞"}],software:[{label:"服务器软件",value:"server",description:"包含 Apache、Nginx、Tomcat、IIS 等服务器软件"},{label:"数据库",value:"database",description:"包含 MySQL、PostgreSQL、MongoDB 等数据库"},{label:"操作系统",value:"os",description:"包含 Windows、Linux、macOS 等操作系统"},{label:"浏览器",value:"browser",description:"包含 Chrome、Firefox、Safari、Edge 等浏览器"}],language:[{label:"PHP",value:"php",description:"包含 PHP 相关漏洞"},{label:"Java",value:"java",description:"包含 Java 相关漏洞"},{label:"Python",value:"python",description:"包含 Python 相关漏洞"},{label:"JavaScript",value:"javascript",description:"包含 JavaScript 相关漏洞"},{label:"C/C++",value:"c_cpp",description:"包含 C/C++ 相关漏洞"},{label:".NET",value:"dotnet",description:"包含 .NET 相关漏洞"}],severity:[{label:"严重 (Critical)",value:"critical",description:"严重程度为 Critical 的漏洞"},{label:"高危 (High)",value:"high",description:"严重程度为 High 的漏洞"},{label:"中危 (Medium)",value:"medium",description:"严重程度为 Medium 的漏洞"},{label:"低危 (Low)",value:"low",description:"严重程度为 Low 的漏洞"}]},Pr={vulnerability:"漏洞类型",software:"软件类型",language:"开发语言",severity:"严重程度"},Mr={vulnerability:"warning",software:"settings",language:"code",severity:"security"},Dr=({showFilters:r,selectedFilters:s,onFilterChange:n})=>{const h=Object.values(s).reduce((t,x)=>t+x.length,0),l=(t,x,p)=>{const m={...s,[t]:p?[...s[t],x]:s[t].filter(i=>i!==x)};n(m)},o=()=>{n({vulnerability:[],software:[],language:[],severity:[]})};return e.jsx(U,{in:r,children:e.jsxs(c,{sx:{mt:2,p:3,backgroundColor:"rgba(255, 255, 255, 0.95)",borderRadius:2},children:[e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(f,{variant:"h6",sx:{color:"#409eff",display:"flex",alignItems:"center",gap:1},children:"🎯 快速筛选"}),h>0&&e.jsx(C,{size:"small",color:"error",onClick:o,children:"清空筛选"})]}),e.jsx(L,{container:!0,spacing:2,children:Object.entries(Tr).map(([t,x])=>e.jsx(L,{item:!0,xs:12,md:6,children:e.jsxs(Qe,{defaultExpanded:!0,children:[e.jsx(Ne,{expandIcon:e.jsx(W,{}),children:e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx("span",{className:"material-icons",children:Mr[t]}),e.jsx(f,{children:Pr[t]}),s[t].length>0&&e.jsx(we,{badgeContent:s[t].length,color:"primary",sx:{ml:1}})]})}),e.jsx(Ge,{children:e.jsx(Ue,{children:x.map(p=>e.jsx(_e,{control:e.jsx(Ke,{checked:s[t].includes(p.value),onChange:m=>l(t,p.value,m.target.checked)}),label:p.label,title:p.description},p.value))})})]})},t))})]})})};var ee={},Lr=R;Object.defineProperty(ee,"__esModule",{value:!0});var ke=ee.default=void 0,Ar=Lr(I()),Wr=e;ke=ee.default=(0,Ar.default)((0,Wr.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}),"Refresh");var re={},Or=R;Object.defineProperty(re,"__esModule",{value:!0});var Ee=re.default=void 0,qr=Or(I()),Hr=e;Ee=re.default=(0,qr.default)((0,Hr.jsx)("path",{d:"M9.4 16.6 4.8 12l4.6-4.6L8 6l-6 6 6 6zm5.2 0 4.6-4.6-4.6-4.6L16 6l6 6-6 6z"}),"Code");const Br=r=>{if(!r)return"";try{xe.setOptions({breaks:!0,gfm:!0});const s=xe.parse(r);return xr.sanitize(s)}catch(s){return console.error("Markdown 渲染错误:",s),r.replace(/\n/g,"<br>")}},be=r=>{if(!r)return"未知";try{return new Date(r).toLocaleDateString("zh-CN")}catch{return r}},Vr=(r,s=500)=>r?r.length<=s?r:r.substring(0,s)+"...":"",je=r=>({CRITICAL:"error",HIGH:"error",MEDIUM:"warning",LOW:"info"})[(r==null?void 0:r.toUpperCase())||""]||"default",Fr=r=>r?r.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,function(s){let n="json-number";return/^"/.test(s)?/:$/.test(s)?n="json-key":n="json-string":/true|false/.test(s)?n="json-boolean":/null/.test(s)&&(n="json-null"),'<span class="'+n+'">'+s+"</span>"}).replace(/([{}[\],])/g,'<span class="json-punctuation">$1</span>'):"",Jr=r=>{if(!r)return"";try{return JSON.stringify(r,null,2)}catch(s){return console.error("JSON 格式化错误:",s),JSON.stringify(r)}},Qr=({text:r,speed:s=20})=>{const[n,h]=u.useState(""),l=u.useRef(""),o=u.useRef(0),t=u.useRef(null);return u.useEffect(()=>{if(l.current=r,r.length>n.length){t.current&&clearTimeout(t.current);const x=()=>{o.current<l.current.length&&(h(l.current.substring(0,o.current+1)),o.current++,t.current=window.setTimeout(x,s))};o.current<=n.length&&(o.current=n.length,x())}},[r,s,n.length]),u.useEffect(()=>()=>{t.current&&clearTimeout(t.current)},[]),n},Nr=({text:r,speed:s=20})=>{const n=Qr({text:r,speed:s});return e.jsx(c,{sx:{lineHeight:1.6},dangerouslySetInnerHTML:{__html:Br(n)}})},Gr=({cve:r,loadingSummary:s,streamingSummary:n,onGenerateSummary:h,onRefreshSummary:l,onViewRawData:o})=>{var y;const[t,x]=u.useState(!1),[p,m]=u.useState(Date.now()),i=()=>{x(!t)},g=()=>{m(Date.now()),h(r.cve_info.cve_id)},k=()=>{m(Date.now()),l(r.cve_info.cve_id)},j=()=>{o(r.cve_info.cve_id)};return e.jsx(ve,{sx:{mb:3,borderRadius:3,overflow:"hidden"},children:e.jsxs(ye,{children:[e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(c,{sx:{flex:1},children:[e.jsx(f,{variant:"h5",sx:{color:"#e74c3c",fontWeight:"bold",mb:1},children:r.cve_info.cve_id}),e.jsxs(H,{direction:"row",spacing:1,flexWrap:"wrap",sx:{mb:1},children:[e.jsx(S,{label:`发布: ${be(r.cve_info.published_date)}`,size:"small",color:"info"}),e.jsx(S,{label:`修改: ${be(r.cve_info.last_modified_date)}`,size:"small",color:"default"}),r.cve_info.cvss_score&&e.jsx(S,{label:`CVSS: ${r.cve_info.cvss_score}`,size:"small",color:je(r.cve_info.cvss_severity)}),r.cve_info.cvss_severity&&e.jsx(S,{label:r.cve_info.cvss_severity,size:"small",color:je(r.cve_info.cvss_severity)})]})]}),e.jsxs(c,{sx:{display:"flex",gap:1},children:[e.jsx(Q,{onClick:j,size:"small",title:"查看原始 JSON 数据",children:e.jsx(Ee,{})}),e.jsx(Q,{onClick:i,size:"small",sx:{transform:t?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s"},children:e.jsx(W,{})})]})]}),r.cve_info.title&&e.jsx(f,{variant:"subtitle1",sx:{mb:1,fontWeight:"bold",color:"text.secondary"},children:r.cve_info.title}),e.jsx(f,{variant:"body1",sx:{mb:2,lineHeight:1.6},children:Vr(r.cve_info.description,300)}),e.jsx(U,{in:t,children:e.jsxs(c,{sx:{mt:2},children:[e.jsx(fe,{sx:{mb:2}}),e.jsx(f,{variant:"body1",sx:{mb:2,lineHeight:1.6},children:r.cve_info.description}),(r.cve_info.cwe_ids||[]).length>0&&e.jsxs(c,{sx:{mb:2},children:[e.jsx(f,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"CWE 类型:"}),e.jsx(H,{direction:"row",spacing:1,flexWrap:"wrap",children:(r.cve_info.cwe_ids||[]).map((v,w)=>e.jsx(S,{label:v,size:"small",variant:"outlined"},w))})]}),(r.cve_info.affected_products||[]).length>0&&e.jsxs(c,{sx:{mb:2},children:[e.jsx(f,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"受影响的产品:"}),e.jsxs(H,{direction:"row",spacing:1,flexWrap:"wrap",children:[(r.cve_info.affected_products||[]).slice(0,10).map((v,w)=>e.jsx(S,{label:v,size:"small",color:"warning"},w)),(r.cve_info.affected_products||[]).length>10&&e.jsx(S,{label:`+${r.cve_info.affected_products.length-10} 更多`,size:"small",variant:"outlined"})]})]}),(r.cve_info.references||[]).length>0&&e.jsxs(c,{sx:{mb:2},children:[e.jsx(f,{variant:"subtitle2",sx:{mb:1,fontWeight:"bold"},children:"参考链接:"}),e.jsxs(c,{sx:{maxHeight:100,overflow:"auto"},children:[(r.cve_info.references||[]).slice(0,5).map((v,w)=>e.jsx(f,{variant:"body2",sx:{mb:.5},children:e.jsx("a",{href:v,target:"_blank",rel:"noopener noreferrer",style:{color:"#409eff"},children:v})},w)),(r.cve_info.references||[]).length>5&&e.jsxs(f,{variant:"body2",color:"text.secondary",children:["... 还有 ",r.cve_info.references.length-5," 个链接"]})]})]})]})}),e.jsx(fe,{sx:{my:2}}),e.jsxs(c,{children:[e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(f,{variant:"h6",sx:{color:"#409eff",display:"flex",alignItems:"center",gap:1},children:"🤖 AI 总结"}),e.jsxs(c,{sx:{display:"flex",gap:1},children:[r.summary&&e.jsx(C,{size:"small",startIcon:e.jsx(ke,{}),onClick:k,disabled:s,children:"刷新"}),!r.summary&&!n&&e.jsx(C,{size:"small",variant:"contained",onClick:g,disabled:s,startIcon:s?e.jsx(A,{size:16}):null,children:"生成总结"})]})]}),s&&!n&&e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:2,p:2},children:[e.jsx(A,{size:20}),e.jsx(f,{variant:"body2",color:"text.secondary",children:"正在生成 AI 总结..."})]}),(n||r.summary&&!n)&&e.jsx(c,{sx:{p:2,backgroundColor:"#f8f9fa",borderRadius:2,border:"1px solid #e9ecef"},children:e.jsx(Nr,{text:n||((y=r.summary)==null?void 0:y.summary)||"",speed:20},`${r.cve_info.cve_id}-${p}-${n?"stream":"cache"}`)}),!r.summary&&!n&&!s&&e.jsx(f,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:'点击"生成总结"按钮获取 AI 分析'})]})]})})};var se={},Ur=R;Object.defineProperty(se,"__esModule",{value:!0});var $e=se.default=void 0,Kr=Ur(I()),Xr=e;$e=se.default=(0,Kr.default)((0,Xr.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");const Yr=({jsonModal:r,onClose:s,onToggleWordWrap:n})=>{const h=r.data?Jr(r.data):"",l=Fr(h);return e.jsxs(Xe,{open:r.open,onClose:s,maxWidth:"lg",fullWidth:!0,PaperProps:{sx:{height:"80vh",backgroundColor:"#1e1e1e",color:"white"}},children:[e.jsxs(Ye,{sx:{backgroundColor:"#2d2d2d",color:"#61dafb",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #404040"},children:[e.jsxs(f,{variant:"h6",children:["CVE 原始数据: ",r.cveId]}),e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(_e,{control:e.jsx(Ze,{checked:r.wordWrap,onChange:n,size:"small",sx:{"& .MuiSwitch-switchBase.Mui-checked":{color:"#4caf50"},"& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":{backgroundColor:"#4caf50"}}}),label:e.jsx(f,{variant:"body2",sx:{color:"#ccc"},children:"自动换行"})}),e.jsx(Q,{onClick:s,size:"small",sx:{color:"#ccc","&:hover":{backgroundColor:"#404040",color:"white"}},children:e.jsx($e,{})})]})]}),e.jsx(er,{sx:{p:0,backgroundColor:"#1e1e1e",overflow:"auto"},children:r.loading?e.jsxs(c,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:2},children:[e.jsx(A,{sx:{color:"#61dafb"}}),e.jsx(f,{sx:{color:"#ccc"},children:"正在加载原始数据..."})]}):e.jsx(c,{sx:{fontFamily:'"Courier New", "Monaco", "Menlo", monospace',fontSize:"14px",lineHeight:1.4,p:2,whiteSpace:r.wordWrap?"pre-wrap":"pre",wordBreak:r.wordWrap?"break-word":"normal",overflow:"auto"},dangerouslySetInnerHTML:{__html:l}})}),e.jsx(rr,{sx:{backgroundColor:"#2d2d2d",borderTop:"1px solid #404040"},children:e.jsx(C,{onClick:s,sx:{color:"#ccc","&:hover":{backgroundColor:"#404040"}},children:"关闭"})})]})},Zr=sr({palette:{primary:{main:"#409eff"},secondary:{main:"#67c23a"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif'}}),es=()=>{const[r,s]=u.useState(""),[n,h]=u.useState(20),[l,o]=u.useState(1),{searchResults:t,searching:x,hasSearched:p,setSearchResults:m,performSearch:i}=Ir(),[g,k]=u.useState(!0),[j,y]=u.useState(!1),[v,w]=u.useState(!1),[z,Re]=u.useState({vulnerability:[],software:[],language:[],severity:[]}),[Ie,te]=u.useState({}),[ze,O]=u.useState({}),[V,ae]=u.useState({open:!1,message:"",severity:"info"}),[Te,M]=u.useState({open:!1,cveId:"",data:null,loading:!1,wordWrap:!0}),Pe=[10,20,50,100],T=(a,d="info")=>{ae({open:!0,message:a,severity:d})},ne=()=>{ae(a=>({...a,open:!1}))},oe=u.useCallback(async(a=!0)=>{try{const d=await i(r,z,l,n,a);a&&(o(1),k(!1),y(!1),T(`找到 ${d.total_results} 个结果`,"success"))}catch(d){T(d.message,"error")}},[r,z,l,n,i]),Me=(a,d)=>{o(d),p&&i(r,z,d,n,!1)},De=a=>{h(a),o(1),p&&r.trim()&&i(r,z,1,a,!1)},Le=a=>{s(a),k(!1)},Ae=a=>{Re(a)},ie=async a=>{te(d=>({...d,[a]:!0})),O(d=>({...d,[a]:""}));try{const d=await B.getCVESummaryStream(a);if(!d)throw new Error("无法获取流式响应");const E=d.getReader(),q=new TextDecoder;let F="",ce=!1;for(;;){const{done:Ve,value:Fe}=await E.read();if(Ve)break;F+=q.decode(Fe,{stream:!0});const de=F.split(`
`);F=de.pop()||"";for(const ue of de)if(ue.startsWith("data: "))try{const _=JSON.parse(ue.slice(6));if(_.type==="cache"){ce=!0;const P=_.data;if(t&&t.results){const D=t.results.map(b=>b.cve_info.cve_id===a?{...b,summary:P}:b);m(b=>b?{...b,results:D}:null)}T("从缓存获取 AI 总结","info");break}else if(_.type==="chunk")O(P=>({...P,[a]:(P[a]||"")+_.data}));else if(_.type==="complete"){const P=_.data;if(t&&t.results){const D=t.results.map(b=>b.cve_info.cve_id===a?{...b,summary:P}:b);m(b=>b?{...b,results:D}:null)}O(D=>{const b={...D};return delete b[a],b}),T("AI 总结生成成功","success")}else if(_.type==="error")throw new Error(_.data)}catch(_){console.error("解析流式数据错误:",_)}if(ce)break}}catch(d){console.error("生成总结错误:",d),T(`生成总结失败: ${d.message}`,"error"),O(E=>{const q={...E};return delete q[a],q})}finally{te(d=>({...d,[a]:!1}))}},We=async a=>{await ie(a)},Oe=async a=>{M(d=>({...d,open:!0,cveId:a,loading:!0,data:null}));try{const d=await B.getCVERawData(a);M(E=>({...E,data:d.raw_data,loading:!1}))}catch(d){console.error("获取原始数据错误:",d),T(`获取原始数据失败: ${d.message}`,"error"),M(E=>({...E,open:!1,loading:!1}))}},qe=()=>{M({open:!1,cveId:"",data:null,loading:!1,wordWrap:!0})},He=()=>{M(a=>({...a,wordWrap:!a.wordWrap}))};u.useEffect(()=>{const a=()=>{w(window.scrollY>300)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]);const Be=()=>{window.scrollTo({top:0,behavior:"smooth"})},le=Object.values(z).reduce((a,d)=>a+d.length,0);return e.jsxs(tr,{theme:Zr,children:[e.jsx(ar,{}),e.jsxs("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",padding:"20px"},children:[e.jsxs(c,{sx:{textAlign:"center",color:"white",mb:5},children:[e.jsx(f,{variant:"h2",component:"h1",sx:{textShadow:"2px 2px 4px rgba(0,0,0,0.3)"},children:"🔍 CVE 搜索服务"}),e.jsx(f,{variant:"h6",sx:{opacity:.9,mt:1},children:"基于 AI 的漏洞信息搜索与总结平台"})]}),e.jsx(nr,{maxWidth:"lg",children:e.jsxs(he,{elevation:3,sx:{borderRadius:"20px",overflow:"hidden"},children:[e.jsxs(c,{sx:{p:4,background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"},children:[e.jsx(or,{fullWidth:!0,variant:"outlined",placeholder:"输入搜索条件，例如：remote code execution",value:r,onChange:a=>s(a.target.value),onKeyPress:a=>a.key==="Enter"&&oe(),InputProps:{startAdornment:e.jsx(pe,{position:"start",children:e.jsx(Se,{})}),endAdornment:e.jsx(pe,{position:"end",children:e.jsx(C,{variant:"contained",onClick:()=>oe(),disabled:x,startIcon:x?e.jsx(A,{size:20}):null,children:"搜索"})})},sx:{mb:2}}),e.jsx(he,{sx:{p:2,backgroundColor:"rgba(255, 255, 255, 0.8)"},children:e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2},children:[e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(f,{variant:"body2",children:"每页显示："}),e.jsx(ir,{size:"small",children:Pe.map(a=>e.jsx(C,{variant:n===a?"contained":"outlined",onClick:()=>De(a),children:a},a))})]}),e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsxs(C,{size:"small",variant:j?"contained":"outlined",onClick:()=>y(!j),startIcon:j?e.jsx(G,{}):e.jsx(W,{}),endIcon:le>0?e.jsx(we,{badgeContent:le,color:"primary"}):null,children:[j?"隐藏":"显示","筛选器"]}),e.jsxs(C,{size:"small",variant:g?"contained":"outlined",onClick:()=>k(!g),startIcon:g?e.jsx(G,{}):e.jsx(W,{}),children:[g?"隐藏":"显示","搜索示例"]})]})]})}),e.jsx(Dr,{showFilters:j,selectedFilters:z,onFilterChange:Ae}),e.jsx(zr,{showExamples:g,onExampleClick:Le})]}),p&&e.jsx(c,{sx:{p:4},children:x?e.jsxs(c,{sx:{textAlign:"center",p:4},children:[e.jsx(A,{size:40}),e.jsx(f,{sx:{mt:2},children:"正在搜索中..."})]}):t?e.jsxs(e.Fragment,{children:[e.jsx(c,{sx:{p:2,backgroundColor:"rgba(255, 255, 255, 0.9)",borderRadius:2,mb:2},children:e.jsxs(L,{container:!0,justifyContent:"space-between",alignItems:"center",children:[e.jsx(L,{item:!0,xs:12,md:8,children:e.jsxs(H,{direction:"row",spacing:1,flexWrap:"wrap",children:[e.jsx(S,{icon:e.jsx("span",{className:"material-icons",children:"check_circle"}),label:`共找到 ${t.total_results} 个 CVE`,color:"success",size:"medium"}),e.jsx(S,{label:`第 ${t.page} / ${t.total_pages} 页`,color:"info"}),e.jsx(S,{label:`显示 ${(t.page-1)*t.page_size+1}-${Math.min(t.page*t.page_size,t.total_results)}`,color:"warning"})]})}),e.jsx(L,{item:!0,xs:12,md:4,sx:{textAlign:{xs:"left",md:"right"},mt:{xs:1,md:0}},children:e.jsxs(f,{variant:"body2",color:"text.secondary",children:["搜索耗时: ",t.search_time.toFixed(3),"s"]})})]})}),t.results.map(a=>e.jsx(Gr,{cve:a,loadingSummary:Ie[a.cve_info.cve_id]||!1,streamingSummary:ze[a.cve_info.cve_id],onGenerateSummary:ie,onRefreshSummary:We,onViewRawData:Oe},a.cve_info.cve_id)),t.total_pages>1&&e.jsx(c,{sx:{display:"flex",justifyContent:"center",mt:4},children:e.jsx(lr,{count:t.total_pages,page:l,onChange:Me,color:"primary",size:"large",showFirstButton:!0,showLastButton:!0})})]}):null})]})}),v&&e.jsx(cr,{color:"primary",size:"medium",onClick:Be,sx:{position:"fixed",bottom:24,right:24,zIndex:1e3},children:e.jsx(Ce,{})}),e.jsx(Yr,{jsonModal:Te,onClose:qe,onToggleWordWrap:He}),e.jsx(dr,{open:V.open,autoHideDuration:6e3,onClose:ne,anchorOrigin:{vertical:"top",horizontal:"center"},children:e.jsx(ur,{onClose:ne,severity:V.severity,sx:{width:"100%"},children:V.message})})]})]})};N.createRoot(document.getElementById("root")).render(e.jsx(pr.StrictMode,{children:e.jsx(es,{})}));
