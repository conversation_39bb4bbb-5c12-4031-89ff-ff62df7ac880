var Tn=Object.defineProperty;var xn=(l,e,t)=>e in l?Tn(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var E=(l,e,t)=>(xn(l,typeof e!="symbol"?e+"":e,t),t),wn=(l,e,t)=>{if(!e.has(l))throw TypeError("Cannot "+t)};var Je=(l,e,t)=>{if(e.has(l))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(l):e.set(l,t)};var Le=(l,e,t)=>(wn(l,e,"access private method"),t);function at(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let te=at();function jt(l){te=l}const Xt=/[&<>"']/,En=new RegExp(Xt.source,"g"),Qt=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,An=new RegExp(Qt.source,"g"),yn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},zt=l=>yn[l];function F(l,e){if(e){if(Xt.test(l))return l.replace(En,zt)}else if(Qt.test(l))return l.replace(An,zt);return l}const Sn=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Rn(l){return l.replace(Sn,(e,t)=>(t=t.toLowerCase(),t==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const In=/(^|[^\[])\^/g;function x(l,e){l=typeof l=="string"?l:l.source,e=e||"";const t={replace:(n,i)=>(i=typeof i=="object"&&"source"in i?i.source:i,i=i.replace(In,"$1"),l=l.replace(n,i),t),getRegex:()=>new RegExp(l,e)};return t}function Pt(l){try{l=encodeURI(l).replace(/%25/g,"%")}catch{return null}return l}const Ne={exec:()=>null};function $t(l,e){const t=l.replace(/\|/g,(r,o,a)=>{let c=!1,d=o;for(;--d>=0&&a[d]==="\\";)c=!c;return c?"|":" |"}),n=t.split(/ \|/);let i=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),e)if(n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;i<n.length;i++)n[i]=n[i].trim().replace(/\\\|/g,"|");return n}function Oe(l,e,t){const n=l.length;if(n===0)return"";let i=0;for(;i<n;){const r=l.charAt(n-i-1);if(r===e&&!t)i++;else if(r!==e&&t)i++;else break}return l.slice(0,n-i)}function Ln(l,e){if(l.indexOf(e[1])===-1)return-1;let t=0;for(let n=0;n<l.length;n++)if(l[n]==="\\")n++;else if(l[n]===e[0])t++;else if(l[n]===e[1]&&(t--,t<0))return n;return-1}function vt(l,e,t,n){const i=e.href,r=e.title?F(e.title):null,o=l[1].replace(/\\([\[\]])/g,"$1");if(l[0].charAt(0)!=="!"){n.state.inLink=!0;const a={type:"link",raw:t,href:i,title:r,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,a}return{type:"image",raw:t,href:i,title:r,text:F(o)}}function On(l,e){const t=l.match(/^(\s+)(?:```)/);if(t===null)return e;const n=t[1];return e.split(`
`).map(i=>{const r=i.match(/^\s+/);if(r===null)return i;const[o]=r;return o.length>=n.length?i.slice(n.length):i}).join(`
`)}class ze{constructor(e){E(this,"options");E(this,"rules");E(this,"lexer");this.options=e||te}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:Oe(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],i=On(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const i=Oe(n,"#");(this.options.pedantic||!i||/ $/.test(i))&&(n=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=Oe(t[0].replace(/^ *>[ \t]?/gm,""),`
`),i=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(n);return this.lexer.state.top=i,{type:"blockquote",raw:t[0],tokens:r,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const i=n.length>1,r={type:"list",raw:"",ordered:i,start:i?+n.slice(0,-1):"",loose:!1,items:[]};n=i?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=i?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",c="",d=!1;for(;e;){let h=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;a=t[0],e=e.substring(a.length);let _=t[2].split(`
`,1)[0].replace(/^\t+/,ce=>" ".repeat(3*ce.length)),k=e.split(`
`,1)[0],T=0;this.options.pedantic?(T=2,c=_.trimStart()):(T=t[2].search(/[^ ]/),T=T>4?1:T,c=_.slice(T),T+=t[1].length);let $=!1;if(!_&&/^ *$/.test(k)&&(a+=k+`
`,e=e.substring(k.length+1),h=!0),!h){const ce=new RegExp(`^ {0,${Math.min(3,T-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),xe=new RegExp(`^ {0,${Math.min(3,T-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),K=new RegExp(`^ {0,${Math.min(3,T-1)}}(?:\`\`\`|~~~)`),R=new RegExp(`^ {0,${Math.min(3,T-1)}}#`);for(;e;){const Y=e.split(`
`,1)[0];if(k=Y,this.options.pedantic&&(k=k.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),K.test(k)||R.test(k)||ce.test(k)||xe.test(e))break;if(k.search(/[^ ]/)>=T||!k.trim())c+=`
`+k.slice(T);else{if($||_.search(/[^ ]/)>=4||K.test(_)||R.test(_)||xe.test(_))break;c+=`
`+k}!$&&!k.trim()&&($=!0),a+=Y+`
`,e=e.substring(Y.length+1),_=k.slice(T)}}r.loose||(d?r.loose=!0:/\n *\n *$/.test(a)&&(d=!0));let U=null,Z;this.options.gfm&&(U=/^\[[ xX]\] /.exec(c),U&&(Z=U[0]!=="[ ] ",c=c.replace(/^\[[ xX]\] +/,""))),r.items.push({type:"list_item",raw:a,task:!!U,checked:Z,loose:!1,text:c,tokens:[]}),r.raw+=a}r.items[r.items.length-1].raw=a.trimEnd(),r.items[r.items.length-1].text=c.trimEnd(),r.raw=r.raw.trimEnd();for(let h=0;h<r.items.length;h++)if(this.lexer.state.top=!1,r.items[h].tokens=this.lexer.blockTokens(r.items[h].text,[]),!r.loose){const _=r.items[h].tokens.filter(T=>T.type==="space"),k=_.length>0&&_.some(T=>/\n.*\n/.test(T.raw));r.loose=k}if(r.loose)for(let h=0;h<r.items.length;h++)r.items[h].loose=!0;return r}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),i=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:i,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(t){if(!/[:|]/.test(t[2]))return;const n={type:"table",raw:t[0],header:$t(t[1]).map(i=>({text:i,tokens:[]})),align:t[2].replace(/^\||\| *$/g,"").split("|"),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(n.header.length===n.align.length){let i=n.align.length,r,o,a,c;for(r=0;r<i;r++){const d=n.align[r];d&&(/^ *-+: *$/.test(d)?n.align[r]="right":/^ *:-+: *$/.test(d)?n.align[r]="center":/^ *:-+ *$/.test(d)?n.align[r]="left":n.align[r]=null)}for(i=n.rows.length,r=0;r<i;r++)n.rows[r]=$t(n.rows[r],n.header.length).map(d=>({text:d,tokens:[]}));for(i=n.header.length,o=0;o<i;o++)n.header[o].tokens=this.lexer.inline(n.header[o].text);for(i=n.rows.length,o=0;o<i;o++)for(c=n.rows[o],a=0;a<c.length;a++)c[a].tokens=this.lexer.inline(c[a].text);return n}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:F(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=Oe(n.slice(0,-1),"\\");if((n.length-o.length)%2===0)return}else{const o=Ln(t[2],"()");if(o>-1){const c=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,c).trim(),t[3]=""}}let i=t[2],r="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);o&&(i=o[1],r=o[3])}else r=t[3]?t[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(n)?i=i.slice(1):i=i.slice(1,-1)),vt(t,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:r&&r.replace(this.rules.inline._escapes,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let i=(n[2]||n[1]).replace(/\s+/g," ");if(i=t[i.toLowerCase()],!i){const r=n[0].charAt(0);return{type:"text",raw:r,text:r}}return vt(n,i,n[0],this.lexer)}}emStrong(e,t,n=""){let i=this.rules.inline.emStrong.lDelim.exec(e);if(!i||i[3]&&n.match(/[\p{L}\p{N}]/u))return;if(!(i[1]||i[2]||"")||!n||this.rules.inline.punctuation.exec(n)){const o=[...i[0]].length-1;let a,c,d=o,h=0;const _=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(_.lastIndex=0,t=t.slice(-1*e.length+o);(i=_.exec(t))!=null;){if(a=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!a)continue;if(c=[...a].length,i[3]||i[4]){d+=c;continue}else if((i[5]||i[6])&&o%3&&!((o+c)%3)){h+=c;continue}if(d-=c,d>0)continue;c=Math.min(c,c+d+h);const k=[...i[0]][0].length,T=e.slice(0,o+i.index+k+c);if(Math.min(o,c)%2){const U=T.slice(1,-1);return{type:"em",raw:T,text:U,tokens:this.lexer.inlineTokens(U)}}const $=T.slice(2,-2);return{type:"strong",raw:T,text:$,tokens:this.lexer.inlineTokens($)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const i=/[^ ]/.test(n),r=/^ /.test(n)&&/ $/.test(n);return i&&r&&(n=n.substring(1,n.length-1)),n=F(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,i;return t[2]==="@"?(n=F(t[1]),i="mailto:"+n):(n=F(t[1]),i=n),{type:"link",raw:t[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let n,i;if(t[2]==="@")n=F(t[0]),i="mailto:"+n;else{let r;do r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])[0];while(r!==t[0]);n=F(t[0]),t[1]==="www."?i="http://"+t[0]:i=t[0]}return{type:"link",raw:t[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return this.lexer.state.inRawBlock?n=t[0]:n=F(t[0]),{type:"text",raw:t[0],text:n}}}}const m={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:Ne,lheading:/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};m._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;m._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;m.def=x(m.def).replace("label",m._label).replace("title",m._title).getRegex();m.bullet=/(?:[*+-]|\d{1,9}[.)])/;m.listItemStart=x(/^( *)(bull) */).replace("bull",m.bullet).getRegex();m.list=x(m.list).replace(/bull/g,m.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+m.def.source+")").getRegex();m._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";m._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;m.html=x(m.html,"i").replace("comment",m._comment).replace("tag",m._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();m.lheading=x(m.lheading).replace(/bull/g,m.bullet).getRegex();m.paragraph=x(m._paragraph).replace("hr",m.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.blockquote=x(m.blockquote).replace("paragraph",m.paragraph).getRegex();m.normal={...m};m.gfm={...m.normal,table:"^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};m.gfm.table=x(m.gfm.table).replace("hr",m.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.gfm.paragraph=x(m._paragraph).replace("hr",m.hr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",m.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.pedantic={...m.normal,html:x(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",m._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ne,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:x(m.normal._paragraph).replace("hr",m.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",m.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const p={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:Ne,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,rDelimAst:/^[^_*]*?__[^_*]*?\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\*)[punct](\*+)(?=[\s]|$)|[^punct\s](\*+)(?!\*)(?=[punct\s]|$)|(?!\*)[punct\s](\*+)(?=[^punct\s])|[\s](\*+)(?!\*)(?=[punct])|(?!\*)[punct](\*+)(?!\*)(?=[punct])|[^punct\s](\*+)(?=[^punct\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\s]|$)|[^punct\s](_+)(?!_)(?=[punct\s]|$)|(?!_)[punct\s](_+)(?=[^punct\s])|[\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:Ne,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^((?![*_])[\spunctuation])/};p._punctuation="\\p{P}$+<=>`^|~";p.punctuation=x(p.punctuation,"u").replace(/punctuation/g,p._punctuation).getRegex();p.blockSkip=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g;p.anyPunctuation=/\\[punct]/g;p._escapes=/\\([punct])/g;p._comment=x(m._comment).replace("(?:-->|$)","-->").getRegex();p.emStrong.lDelim=x(p.emStrong.lDelim,"u").replace(/punct/g,p._punctuation).getRegex();p.emStrong.rDelimAst=x(p.emStrong.rDelimAst,"gu").replace(/punct/g,p._punctuation).getRegex();p.emStrong.rDelimUnd=x(p.emStrong.rDelimUnd,"gu").replace(/punct/g,p._punctuation).getRegex();p.anyPunctuation=x(p.anyPunctuation,"gu").replace(/punct/g,p._punctuation).getRegex();p._escapes=x(p._escapes,"gu").replace(/punct/g,p._punctuation).getRegex();p._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;p._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;p.autolink=x(p.autolink).replace("scheme",p._scheme).replace("email",p._email).getRegex();p._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;p.tag=x(p.tag).replace("comment",p._comment).replace("attribute",p._attribute).getRegex();p._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;p._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;p._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;p.link=x(p.link).replace("label",p._label).replace("href",p._href).replace("title",p._title).getRegex();p.reflink=x(p.reflink).replace("label",p._label).replace("ref",m._label).getRegex();p.nolink=x(p.nolink).replace("ref",m._label).getRegex();p.reflinkSearch=x(p.reflinkSearch,"g").replace("reflink",p.reflink).replace("nolink",p.nolink).getRegex();p.normal={...p};p.pedantic={...p.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",p._label).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",p._label).getRegex()};p.gfm={...p.normal,escape:x(p.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};p.gfm.url=x(p.gfm.url,"i").replace("email",p.gfm._extended_email).getRegex();p.breaks={...p.gfm,br:x(p.br).replace("{2,}","*").getRegex(),text:x(p.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};class G{constructor(e){E(this,"tokens");E(this,"options");E(this,"state");E(this,"tokenizer");E(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||te,this.options.tokenizer=this.options.tokenizer||new ze,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:m.normal,inline:p.normal};this.options.pedantic?(t.block=m.pedantic,t.inline=p.pedantic):this.options.gfm&&(t.block=m.gfm,this.options.breaks?t.inline=p.breaks:t.inline=p.gfm),this.tokenizer.rules=t}static get rules(){return{block:m,inline:p}}static lex(e,t){return new G(t).lex(e)}static lexInline(e,t){return new G(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);let t;for(;t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e,t=[]){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(a,c,d)=>c+"    ".repeat(d.length));let n,i,r,o;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>(n=a.call({lexer:this},e,t))?(e=e.substring(n.raw.length),t.push(n),!0):!1))){if(n=this.tokenizer.space(e)){e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);continue}if(n=this.tokenizer.code(e)){e=e.substring(n.raw.length),i=t[t.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n);continue}if(n=this.tokenizer.fences(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.heading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.hr(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.blockquote(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.list(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.html(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.def(e)){e=e.substring(n.raw.length),i=t[t.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+n.raw,i.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title});continue}if(n=this.tokenizer.table(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.lheading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(r=e,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const c=e.slice(1);let d;this.options.extensions.startBlock.forEach(h=>{d=h.call({lexer:this},c),typeof d=="number"&&d>=0&&(a=Math.min(a,d))}),a<1/0&&a>=0&&(r=e.substring(0,a+1))}if(this.state.top&&(n=this.tokenizer.paragraph(r))){i=t[t.length-1],o&&i.type==="paragraph"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n),o=r.length!==e.length,e=e.substring(n.raw.length);continue}if(n=this.tokenizer.text(e)){e=e.substring(n.raw.length),i=t[t.length-1],i&&i.type==="text"?(i.raw+=`
`+n.raw,i.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,i,r,o=e,a,c,d;if(this.tokens.links){const h=Object.keys(this.tokens.links);if(h.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(o))!=null;)h.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(o=o.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(o))!=null;)o=o.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(o))!=null;)o=o.slice(0,a.index)+"++"+o.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(c||(d=""),c=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(h=>(n=h.call({lexer:this},e,t))?(e=e.substring(n.raw.length),t.push(n),!0):!1))){if(n=this.tokenizer.escape(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.tag(e)){e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);continue}if(n=this.tokenizer.link(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(n.raw.length),i=t[t.length-1],i&&n.type==="text"&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);continue}if(n=this.tokenizer.emStrong(e,o,d)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.codespan(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.br(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.del(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.autolink(e)){e=e.substring(n.raw.length),t.push(n);continue}if(!this.state.inLink&&(n=this.tokenizer.url(e))){e=e.substring(n.raw.length),t.push(n);continue}if(r=e,this.options.extensions&&this.options.extensions.startInline){let h=1/0;const _=e.slice(1);let k;this.options.extensions.startInline.forEach(T=>{k=T.call({lexer:this},_),typeof k=="number"&&k>=0&&(h=Math.min(h,k))}),h<1/0&&h>=0&&(r=e.substring(0,h+1))}if(n=this.tokenizer.inlineText(r)){e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(d=n.raw.slice(-1)),c=!0,i=t[t.length-1],i&&i.type==="text"?(i.raw+=n.raw,i.text+=n.text):t.push(n);continue}if(e){const h="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(h);break}else throw new Error(h)}}return t}}class Pe{constructor(e){E(this,"options");this.options=e||te}code(e,t,n){var r;const i=(r=(t||"").match(/^\S*/))==null?void 0:r[0];return e=e.replace(/\n$/,"")+`
`,i?'<pre><code class="language-'+F(i)+'">'+(n?e:F(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:F(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const i=t?"ol":"ul",r=t&&n!==1?' start="'+n+'"':"";return"<"+i+r+`>
`+e+"</"+i+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const i=Pt(e);if(i===null)return n;e=i;let r='<a href="'+e+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>",r}image(e,t,n){const i=Pt(e);if(i===null)return n;e=i;let r=`<img src="${e}" alt="${n}"`;return t&&(r+=` title="${t}"`),r+=">",r}text(e){return e}}class ct{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class q{constructor(e){E(this,"options");E(this,"renderer");E(this,"textRenderer");this.options=e||te,this.options.renderer=this.options.renderer||new Pe,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ct}static parse(e,t){return new q(t).parse(e)}static parseInline(e,t){return new q(t).parseInline(e)}parse(e,t=!0){let n="";for(let i=0;i<e.length;i++){const r=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const o=r,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=a||"";continue}}switch(r.type){case"space":continue;case"hr":{n+=this.renderer.hr();continue}case"heading":{const o=r;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Rn(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=r;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=r;let a="",c="";for(let h=0;h<o.header.length;h++)c+=this.renderer.tablecell(this.parseInline(o.header[h].tokens),{header:!0,align:o.align[h]});a+=this.renderer.tablerow(c);let d="";for(let h=0;h<o.rows.length;h++){const _=o.rows[h];c="";for(let k=0;k<_.length;k++)c+=this.renderer.tablecell(this.parseInline(_[k].tokens),{header:!1,align:o.align[k]});d+=this.renderer.tablerow(c)}n+=this.renderer.table(a,d);continue}case"blockquote":{const o=r,a=this.parse(o.tokens);n+=this.renderer.blockquote(a);continue}case"list":{const o=r,a=o.ordered,c=o.start,d=o.loose;let h="";for(let _=0;_<o.items.length;_++){const k=o.items[_],T=k.checked,$=k.task;let U="";if(k.task){const Z=this.renderer.checkbox(!!T);d?k.tokens.length>0&&k.tokens[0].type==="paragraph"?(k.tokens[0].text=Z+" "+k.tokens[0].text,k.tokens[0].tokens&&k.tokens[0].tokens.length>0&&k.tokens[0].tokens[0].type==="text"&&(k.tokens[0].tokens[0].text=Z+" "+k.tokens[0].tokens[0].text)):k.tokens.unshift({type:"text",text:Z+" "}):U+=Z+" "}U+=this.parse(k.tokens,d),h+=this.renderer.listitem(U,$,!!T)}n+=this.renderer.list(h,a,c);continue}case"html":{const o=r;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=r;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=r,a=o.tokens?this.parseInline(o.tokens):o.text;for(;i+1<e.length&&e[i+1].type==="text";)o=e[++i],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let i=0;i<e.length;i++){const r=e[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const o=this.options.extensions.renderers[r.type].call({parser:this},r);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){n+=o||"";continue}}switch(r.type){case"escape":{const o=r;n+=t.text(o.text);break}case"html":{const o=r;n+=t.html(o.text);break}case"link":{const o=r;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=r;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=r;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=r;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=r;n+=t.codespan(o.text);break}case"br":{n+=t.br();break}case"del":{const o=r;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=r;n+=t.text(o.text);break}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class be{constructor(e){E(this,"options");this.options=e||te}preprocess(e){return e}postprocess(e){return e}}E(be,"passThroughHooks",new Set(["preprocess","postprocess"]));var Te,rt,$e,Vt;class Dn{constructor(...e){Je(this,Te);Je(this,$e);E(this,"defaults",at());E(this,"options",this.setOptions);E(this,"parse",Le(this,Te,rt).call(this,G.lex,q.parse));E(this,"parseInline",Le(this,Te,rt).call(this,G.lexInline,q.parseInline));E(this,"Parser",q);E(this,"Renderer",Pe);E(this,"TextRenderer",ct);E(this,"Lexer",G);E(this,"Tokenizer",ze);E(this,"Hooks",be);this.use(...e)}walkTokens(e,t){var i,r;let n=[];for(const o of e)switch(n=n.concat(t.call(this,o)),o.type){case"table":{const a=o;for(const c of a.header)n=n.concat(this.walkTokens(c.tokens,t));for(const c of a.rows)for(const d of c)n=n.concat(this.walkTokens(d.tokens,t));break}case"list":{const a=o;n=n.concat(this.walkTokens(a.items,t));break}default:{const a=o;(r=(i=this.defaults.extensions)==null?void 0:i.childTokens)!=null&&r[a.type]?this.defaults.extensions.childTokens[a.type].forEach(c=>{n=n.concat(this.walkTokens(a[c],t))}):a.tokens&&(n=n.concat(this.walkTokens(a.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{const i={...n};if(i.async=this.defaults.async||i.async||!1,n.extensions&&(n.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const o=t.renderers[r.name];o?t.renderers[r.name]=function(...a){let c=r.renderer.apply(this,a);return c===!1&&(c=o.apply(this,a)),c}:t.renderers[r.name]=r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=t[r.level];o?o.unshift(r.tokenizer):t[r.level]=[r.tokenizer],r.start&&(r.level==="block"?t.startBlock?t.startBlock.push(r.start):t.startBlock=[r.start]:r.level==="inline"&&(t.startInline?t.startInline.push(r.start):t.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(t.childTokens[r.name]=r.childTokens)}),i.extensions=t),n.renderer){const r=this.defaults.renderer||new Pe(this.defaults);for(const o in n.renderer){const a=n.renderer[o],c=o,d=r[c];r[c]=(...h)=>{let _=a.apply(r,h);return _===!1&&(_=d.apply(r,h)),_||""}}i.renderer=r}if(n.tokenizer){const r=this.defaults.tokenizer||new ze(this.defaults);for(const o in n.tokenizer){const a=n.tokenizer[o],c=o,d=r[c];r[c]=(...h)=>{let _=a.apply(r,h);return _===!1&&(_=d.apply(r,h)),_}}i.tokenizer=r}if(n.hooks){const r=this.defaults.hooks||new be;for(const o in n.hooks){const a=n.hooks[o],c=o,d=r[c];be.passThroughHooks.has(o)?r[c]=h=>{if(this.defaults.async)return Promise.resolve(a.call(r,h)).then(k=>d.call(r,k));const _=a.call(r,h);return d.call(r,_)}:r[c]=(...h)=>{let _=a.apply(r,h);return _===!1&&(_=d.apply(r,h)),_}}i.hooks=r}if(n.walkTokens){const r=this.defaults.walkTokens,o=n.walkTokens;i.walkTokens=function(a){let c=[];return c.push(o.call(this,a)),r&&(c=c.concat(r.call(this,a))),c}}this.defaults={...this.defaults,...i}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return G.lex(e,t??this.defaults)}parser(e,t){return q.parse(e,t??this.defaults)}}Te=new WeakSet,rt=function(e,t){return(n,i)=>{const r={...i},o={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(o.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),o.async=!0);const a=Le(this,$e,Vt).call(this,!!o.silent,!!o.async);if(typeof n>"u"||n===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(o.hooks&&(o.hooks.options=o),o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(n):n).then(c=>e(c,o)).then(c=>o.walkTokens?Promise.all(this.walkTokens(c,o.walkTokens)).then(()=>c):c).then(c=>t(c,o)).then(c=>o.hooks?o.hooks.postprocess(c):c).catch(a);try{o.hooks&&(n=o.hooks.preprocess(n));const c=e(n,o);o.walkTokens&&this.walkTokens(c,o.walkTokens);let d=t(c,o);return o.hooks&&(d=o.hooks.postprocess(d)),d}catch(c){return a(c)}}},$e=new WeakSet,Vt=function(e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+F(n.message+"",!0)+"</pre>";return t?Promise.resolve(i):i}if(t)return Promise.reject(n);throw n}};const ee=new Dn;function w(l,e){return ee.parse(l,e)}w.options=w.setOptions=function(l){return ee.setOptions(l),w.defaults=ee.defaults,jt(w.defaults),w};w.getDefaults=at;w.defaults=te;w.use=function(...l){return ee.use(...l),w.defaults=ee.defaults,jt(w.defaults),w};w.walkTokens=function(l,e){return ee.walkTokens(l,e)};w.parseInline=ee.parseInline;w.Parser=q;w.parser=q.parse;w.Renderer=Pe;w.TextRenderer=ct;w.Lexer=G;w.lexer=G.lex;w.Tokenizer=ze;w.Hooks=be;w.parse=w;w.options;w.setOptions;w.use;w.walkTokens;w.parseInline;q.parse;G.lex;/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Kt,setPrototypeOf:Ut,isFrozen:Cn,getPrototypeOf:Mn,getOwnPropertyDescriptor:Nn}=Object;let{freeze:z,seal:H,create:Jt}=Object,{apply:ot,construct:lt}=typeof Reflect<"u"&&Reflect;z||(z=function(e){return e});H||(H=function(e){return e});ot||(ot=function(e,t,n){return e.apply(t,n)});lt||(lt=function(e,t){return new e(...t)});const De=P(Array.prototype.forEach),zn=P(Array.prototype.lastIndexOf),Ft=P(Array.prototype.pop),ge=P(Array.prototype.push),Pn=P(Array.prototype.splice),Me=P(String.prototype.toLowerCase),et=P(String.prototype.toString),Ht=P(String.prototype.match),me=P(String.prototype.replace),$n=P(String.prototype.indexOf),vn=P(String.prototype.trim),B=P(Object.prototype.hasOwnProperty),N=P(RegExp.prototype.test),de=Un(TypeError);function P(l){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return ot(l,e,n)}}function Un(l){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return lt(l,t)}}function b(l,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Me;Ut&&Ut(l,null);let n=e.length;for(;n--;){let i=e[n];if(typeof i=="string"){const r=t(i);r!==i&&(Cn(e)||(e[n]=r),i=r)}l[i]=!0}return l}function Fn(l){for(let e=0;e<l.length;e++)B(l,e)||(l[e]=null);return l}function V(l){const e=Jt(null);for(const[t,n]of Kt(l))B(l,t)&&(Array.isArray(n)?e[t]=Fn(n):n&&typeof n=="object"&&n.constructor===Object?e[t]=V(n):e[t]=n);return e}function ke(l,e){for(;l!==null;){const n=Nn(l,e);if(n){if(n.get)return P(n.get);if(typeof n.value=="function")return P(n.value)}l=Mn(l)}function t(){return null}return t}const Bt=z(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),tt=z(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),nt=z(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Hn=z(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),it=z(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Bn=z(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Wt=z(["#text"]),Gt=z(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),st=z(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),qt=z(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ce=z(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Wn=H(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Gn=H(/<%[\w\W]*|[\w\W]*%>/gm),qn=H(/\$\{[\w\W]*/gm),Zn=H(/^data-[\-\w.\u00B7-\uFFFF]+$/),Yn=H(/^aria-[\-\w]+$/),en=H(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),jn=H(/^(?:\w+script|data):/i),Xn=H(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),tn=H(/^html$/i),Qn=H(/^[a-z][.\w]*(-[.\w]+)+$/i);var Zt=Object.freeze({__proto__:null,ARIA_ATTR:Yn,ATTR_WHITESPACE:Xn,CUSTOM_ELEMENT:Qn,DATA_ATTR:Zn,DOCTYPE_NAME:tn,ERB_EXPR:Gn,IS_ALLOWED_URI:en,IS_SCRIPT_OR_DATA:jn,MUSTACHE_EXPR:Wn,TMPLIT_EXPR:qn});const _e={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Vn=function(){return typeof window>"u"?null:window},Kn=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null;const i="data-tt-policy-suffix";t&&t.hasAttribute(i)&&(n=t.getAttribute(i));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+r+" could not be created."),null}},Yt=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function nn(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Vn();const e=g=>nn(g);if(e.version="3.2.6",e.removed=[],!l||!l.document||l.document.nodeType!==_e.document||!l.Element)return e.isSupported=!1,e;let{document:t}=l;const n=t,i=n.currentScript,{DocumentFragment:r,HTMLTemplateElement:o,Node:a,Element:c,NodeFilter:d,NamedNodeMap:h=l.NamedNodeMap||l.MozNamedAttrMap,HTMLFormElement:_,DOMParser:k,trustedTypes:T}=l,$=c.prototype,U=ke($,"cloneNode"),Z=ke($,"remove"),ce=ke($,"nextSibling"),xe=ke($,"childNodes"),K=ke($,"parentNode");if(typeof o=="function"){const g=t.createElement("template");g.content&&g.content.ownerDocument&&(t=g.content.ownerDocument)}let R,Y="";const{implementation:ve,createNodeIterator:sn,createDocumentFragment:rn,getElementsByTagName:on}=t,{importNode:ln}=n;let M=Yt();e.isSupported=typeof Kt=="function"&&typeof K=="function"&&ve&&ve.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:Ue,ERB_EXPR:Fe,TMPLIT_EXPR:He,DATA_ATTR:an,ARIA_ATTR:cn,IS_SCRIPT_OR_DATA:un,ATTR_WHITESPACE:ut,CUSTOM_ELEMENT:pn}=Zt;let{IS_ALLOWED_URI:pt}=Zt,I=null;const ft=b({},[...Bt,...tt,...nt,...it,...Wt]);let O=null;const ht=b({},[...Gt,...st,...qt,...Ce]);let y=Object.seal(Jt(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ue=null,Be=null,gt=!0,We=!0,mt=!1,dt=!0,ne=!1,we=!0,J=!1,Ge=!1,qe=!1,ie=!1,Ee=!1,Ae=!1,kt=!0,_t=!1;const fn="user-content-";let Ze=!0,pe=!1,se={},re=null;const bt=b({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Tt=null;const xt=b({},["audio","video","img","source","image","track"]);let Ye=null;const wt=b({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ye="http://www.w3.org/1998/Math/MathML",Se="http://www.w3.org/2000/svg",j="http://www.w3.org/1999/xhtml";let oe=j,je=!1,Xe=null;const hn=b({},[ye,Se,j],et);let Re=b({},["mi","mo","mn","ms","mtext"]),Ie=b({},["annotation-xml"]);const gn=b({},["title","style","font","a","script"]);let fe=null;const mn=["application/xhtml+xml","text/html"],dn="text/html";let L=null,le=null;const kn=t.createElement("form"),Et=function(s){return s instanceof RegExp||s instanceof Function},Qe=function(){let s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(le&&le===s)){if((!s||typeof s!="object")&&(s={}),s=V(s),fe=mn.indexOf(s.PARSER_MEDIA_TYPE)===-1?dn:s.PARSER_MEDIA_TYPE,L=fe==="application/xhtml+xml"?et:Me,I=B(s,"ALLOWED_TAGS")?b({},s.ALLOWED_TAGS,L):ft,O=B(s,"ALLOWED_ATTR")?b({},s.ALLOWED_ATTR,L):ht,Xe=B(s,"ALLOWED_NAMESPACES")?b({},s.ALLOWED_NAMESPACES,et):hn,Ye=B(s,"ADD_URI_SAFE_ATTR")?b(V(wt),s.ADD_URI_SAFE_ATTR,L):wt,Tt=B(s,"ADD_DATA_URI_TAGS")?b(V(xt),s.ADD_DATA_URI_TAGS,L):xt,re=B(s,"FORBID_CONTENTS")?b({},s.FORBID_CONTENTS,L):bt,ue=B(s,"FORBID_TAGS")?b({},s.FORBID_TAGS,L):V({}),Be=B(s,"FORBID_ATTR")?b({},s.FORBID_ATTR,L):V({}),se=B(s,"USE_PROFILES")?s.USE_PROFILES:!1,gt=s.ALLOW_ARIA_ATTR!==!1,We=s.ALLOW_DATA_ATTR!==!1,mt=s.ALLOW_UNKNOWN_PROTOCOLS||!1,dt=s.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ne=s.SAFE_FOR_TEMPLATES||!1,we=s.SAFE_FOR_XML!==!1,J=s.WHOLE_DOCUMENT||!1,ie=s.RETURN_DOM||!1,Ee=s.RETURN_DOM_FRAGMENT||!1,Ae=s.RETURN_TRUSTED_TYPE||!1,qe=s.FORCE_BODY||!1,kt=s.SANITIZE_DOM!==!1,_t=s.SANITIZE_NAMED_PROPS||!1,Ze=s.KEEP_CONTENT!==!1,pe=s.IN_PLACE||!1,pt=s.ALLOWED_URI_REGEXP||en,oe=s.NAMESPACE||j,Re=s.MATHML_TEXT_INTEGRATION_POINTS||Re,Ie=s.HTML_INTEGRATION_POINTS||Ie,y=s.CUSTOM_ELEMENT_HANDLING||{},s.CUSTOM_ELEMENT_HANDLING&&Et(s.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(y.tagNameCheck=s.CUSTOM_ELEMENT_HANDLING.tagNameCheck),s.CUSTOM_ELEMENT_HANDLING&&Et(s.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(y.attributeNameCheck=s.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),s.CUSTOM_ELEMENT_HANDLING&&typeof s.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(y.allowCustomizedBuiltInElements=s.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ne&&(We=!1),Ee&&(ie=!0),se&&(I=b({},Wt),O=[],se.html===!0&&(b(I,Bt),b(O,Gt)),se.svg===!0&&(b(I,tt),b(O,st),b(O,Ce)),se.svgFilters===!0&&(b(I,nt),b(O,st),b(O,Ce)),se.mathMl===!0&&(b(I,it),b(O,qt),b(O,Ce))),s.ADD_TAGS&&(I===ft&&(I=V(I)),b(I,s.ADD_TAGS,L)),s.ADD_ATTR&&(O===ht&&(O=V(O)),b(O,s.ADD_ATTR,L)),s.ADD_URI_SAFE_ATTR&&b(Ye,s.ADD_URI_SAFE_ATTR,L),s.FORBID_CONTENTS&&(re===bt&&(re=V(re)),b(re,s.FORBID_CONTENTS,L)),Ze&&(I["#text"]=!0),J&&b(I,["html","head","body"]),I.table&&(b(I,["tbody"]),delete ue.tbody),s.TRUSTED_TYPES_POLICY){if(typeof s.TRUSTED_TYPES_POLICY.createHTML!="function")throw de('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof s.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw de('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');R=s.TRUSTED_TYPES_POLICY,Y=R.createHTML("")}else R===void 0&&(R=Kn(T,i)),R!==null&&typeof Y=="string"&&(Y=R.createHTML(""));z&&z(s),le=s}},At=b({},[...tt,...nt,...Hn]),yt=b({},[...it,...Bn]),_n=function(s){let u=K(s);(!u||!u.tagName)&&(u={namespaceURI:oe,tagName:"template"});const f=Me(s.tagName),A=Me(u.tagName);return Xe[s.namespaceURI]?s.namespaceURI===Se?u.namespaceURI===j?f==="svg":u.namespaceURI===ye?f==="svg"&&(A==="annotation-xml"||Re[A]):!!At[f]:s.namespaceURI===ye?u.namespaceURI===j?f==="math":u.namespaceURI===Se?f==="math"&&Ie[A]:!!yt[f]:s.namespaceURI===j?u.namespaceURI===Se&&!Ie[A]||u.namespaceURI===ye&&!Re[A]?!1:!yt[f]&&(gn[f]||!At[f]):!!(fe==="application/xhtml+xml"&&Xe[s.namespaceURI]):!1},W=function(s){ge(e.removed,{element:s});try{K(s).removeChild(s)}catch{Z(s)}},ae=function(s,u){try{ge(e.removed,{attribute:u.getAttributeNode(s),from:u})}catch{ge(e.removed,{attribute:null,from:u})}if(u.removeAttribute(s),s==="is")if(ie||Ee)try{W(u)}catch{}else try{u.setAttribute(s,"")}catch{}},St=function(s){let u=null,f=null;if(qe)s="<remove></remove>"+s;else{const S=Ht(s,/^[\r\n\t ]+/);f=S&&S[0]}fe==="application/xhtml+xml"&&oe===j&&(s='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+s+"</body></html>");const A=R?R.createHTML(s):s;if(oe===j)try{u=new k().parseFromString(A,fe)}catch{}if(!u||!u.documentElement){u=ve.createDocument(oe,"template",null);try{u.documentElement.innerHTML=je?Y:A}catch{}}const D=u.body||u.documentElement;return s&&f&&D.insertBefore(t.createTextNode(f),D.childNodes[0]||null),oe===j?on.call(u,J?"html":"body")[0]:J?u.documentElement:D},Rt=function(s){return sn.call(s.ownerDocument||s,s,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null)},Ve=function(s){return s instanceof _&&(typeof s.nodeName!="string"||typeof s.textContent!="string"||typeof s.removeChild!="function"||!(s.attributes instanceof h)||typeof s.removeAttribute!="function"||typeof s.setAttribute!="function"||typeof s.namespaceURI!="string"||typeof s.insertBefore!="function"||typeof s.hasChildNodes!="function")},It=function(s){return typeof a=="function"&&s instanceof a};function X(g,s,u){De(g,f=>{f.call(e,s,u,le)})}const Lt=function(s){let u=null;if(X(M.beforeSanitizeElements,s,null),Ve(s))return W(s),!0;const f=L(s.nodeName);if(X(M.uponSanitizeElement,s,{tagName:f,allowedTags:I}),we&&s.hasChildNodes()&&!It(s.firstElementChild)&&N(/<[/\w!]/g,s.innerHTML)&&N(/<[/\w!]/g,s.textContent)||s.nodeType===_e.progressingInstruction||we&&s.nodeType===_e.comment&&N(/<[/\w]/g,s.data))return W(s),!0;if(!I[f]||ue[f]){if(!ue[f]&&Dt(f)&&(y.tagNameCheck instanceof RegExp&&N(y.tagNameCheck,f)||y.tagNameCheck instanceof Function&&y.tagNameCheck(f)))return!1;if(Ze&&!re[f]){const A=K(s)||s.parentNode,D=xe(s)||s.childNodes;if(D&&A){const S=D.length;for(let v=S-1;v>=0;--v){const Q=U(D[v],!0);Q.__removalCount=(s.__removalCount||0)+1,A.insertBefore(Q,ce(s))}}}return W(s),!0}return s instanceof c&&!_n(s)||(f==="noscript"||f==="noembed"||f==="noframes")&&N(/<\/no(script|embed|frames)/i,s.innerHTML)?(W(s),!0):(ne&&s.nodeType===_e.text&&(u=s.textContent,De([Ue,Fe,He],A=>{u=me(u,A," ")}),s.textContent!==u&&(ge(e.removed,{element:s.cloneNode()}),s.textContent=u)),X(M.afterSanitizeElements,s,null),!1)},Ot=function(s,u,f){if(kt&&(u==="id"||u==="name")&&(f in t||f in kn))return!1;if(!(We&&!Be[u]&&N(an,u))){if(!(gt&&N(cn,u))){if(!O[u]||Be[u]){if(!(Dt(s)&&(y.tagNameCheck instanceof RegExp&&N(y.tagNameCheck,s)||y.tagNameCheck instanceof Function&&y.tagNameCheck(s))&&(y.attributeNameCheck instanceof RegExp&&N(y.attributeNameCheck,u)||y.attributeNameCheck instanceof Function&&y.attributeNameCheck(u))||u==="is"&&y.allowCustomizedBuiltInElements&&(y.tagNameCheck instanceof RegExp&&N(y.tagNameCheck,f)||y.tagNameCheck instanceof Function&&y.tagNameCheck(f))))return!1}else if(!Ye[u]){if(!N(pt,me(f,ut,""))){if(!((u==="src"||u==="xlink:href"||u==="href")&&s!=="script"&&$n(f,"data:")===0&&Tt[s])){if(!(mt&&!N(un,me(f,ut,"")))){if(f)return!1}}}}}}return!0},Dt=function(s){return s!=="annotation-xml"&&Ht(s,pn)},Ct=function(s){X(M.beforeSanitizeAttributes,s,null);const{attributes:u}=s;if(!u||Ve(s))return;const f={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:O,forceKeepAttr:void 0};let A=u.length;for(;A--;){const D=u[A],{name:S,namespaceURI:v,value:Q}=D,he=L(S),Ke=Q;let C=S==="value"?Ke:vn(Ke);if(f.attrName=he,f.attrValue=C,f.keepAttr=!0,f.forceKeepAttr=void 0,X(M.uponSanitizeAttribute,s,f),C=f.attrValue,_t&&(he==="id"||he==="name")&&(ae(S,s),C=fn+C),we&&N(/((--!?|])>)|<\/(style|title)/i,C)){ae(S,s);continue}if(f.forceKeepAttr)continue;if(!f.keepAttr){ae(S,s);continue}if(!dt&&N(/\/>/i,C)){ae(S,s);continue}ne&&De([Ue,Fe,He],Nt=>{C=me(C,Nt," ")});const Mt=L(s.nodeName);if(!Ot(Mt,he,C)){ae(S,s);continue}if(R&&typeof T=="object"&&typeof T.getAttributeType=="function"&&!v)switch(T.getAttributeType(Mt,he)){case"TrustedHTML":{C=R.createHTML(C);break}case"TrustedScriptURL":{C=R.createScriptURL(C);break}}if(C!==Ke)try{v?s.setAttributeNS(v,S,C):s.setAttribute(S,C),Ve(s)?W(s):Ft(e.removed)}catch{ae(S,s)}}X(M.afterSanitizeAttributes,s,null)},bn=function g(s){let u=null;const f=Rt(s);for(X(M.beforeSanitizeShadowDOM,s,null);u=f.nextNode();)X(M.uponSanitizeShadowNode,u,null),Lt(u),Ct(u),u.content instanceof r&&g(u.content);X(M.afterSanitizeShadowDOM,s,null)};return e.sanitize=function(g){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=null,f=null,A=null,D=null;if(je=!g,je&&(g="<!-->"),typeof g!="string"&&!It(g))if(typeof g.toString=="function"){if(g=g.toString(),typeof g!="string")throw de("dirty is not a string, aborting")}else throw de("toString is not a function");if(!e.isSupported)return g;if(Ge||Qe(s),e.removed=[],typeof g=="string"&&(pe=!1),pe){if(g.nodeName){const Q=L(g.nodeName);if(!I[Q]||ue[Q])throw de("root node is forbidden and cannot be sanitized in-place")}}else if(g instanceof a)u=St("<!---->"),f=u.ownerDocument.importNode(g,!0),f.nodeType===_e.element&&f.nodeName==="BODY"||f.nodeName==="HTML"?u=f:u.appendChild(f);else{if(!ie&&!ne&&!J&&g.indexOf("<")===-1)return R&&Ae?R.createHTML(g):g;if(u=St(g),!u)return ie?null:Ae?Y:""}u&&qe&&W(u.firstChild);const S=Rt(pe?g:u);for(;A=S.nextNode();)Lt(A),Ct(A),A.content instanceof r&&bn(A.content);if(pe)return g;if(ie){if(Ee)for(D=rn.call(u.ownerDocument);u.firstChild;)D.appendChild(u.firstChild);else D=u;return(O.shadowroot||O.shadowrootmode)&&(D=ln.call(n,D,!0)),D}let v=J?u.outerHTML:u.innerHTML;return J&&I["!doctype"]&&u.ownerDocument&&u.ownerDocument.doctype&&u.ownerDocument.doctype.name&&N(tn,u.ownerDocument.doctype.name)&&(v="<!DOCTYPE "+u.ownerDocument.doctype.name+`>
`+v),ne&&De([Ue,Fe,He],Q=>{v=me(v,Q," ")}),R&&Ae?R.createHTML(v):v},e.setConfig=function(){let g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Qe(g),Ge=!0},e.clearConfig=function(){le=null,Ge=!1},e.isValidAttribute=function(g,s,u){le||Qe({});const f=L(g),A=L(s);return Ot(f,A,u)},e.addHook=function(g,s){typeof s=="function"&&ge(M[g],s)},e.removeHook=function(g,s){if(s!==void 0){const u=zn(M[g],s);return u===-1?void 0:Pn(M[g],u,1)[0]}return Ft(M[g])},e.removeHooks=function(g){M[g]=[]},e.removeAllHooks=function(){M=Yt()},e}var ei=nn();export{w as m,ei as p};
