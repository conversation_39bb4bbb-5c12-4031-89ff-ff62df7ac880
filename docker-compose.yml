version: '3.8'

services:
  # 主应用服务 - 前后端分离架构
  cve-search:
    build:
      context: .
      dockerfile: Dockerfile.fullstack
    container_name: cve-search-service
    ports:
      - "8000:8000"
    volumes:
      # 挂载 CVE 数据库目录（请根据实际路径修改）
      - ${CVE_DATA_PATH:-~/Github/cvelistV5}:/app/cve_data:ro
      # 挂载缓存目录
      - ./cache:/app/cache
      # 挂载配置文件
      - ./config:/app/config
      # 挂载日志目录
      - ./logs:/app/logs
    environment:
      # 可以通过环境变量覆盖配置
      - CVE_DIRECTORY=/app/cve_data
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-your-deepseek-api-key-here}
      - DATABASE_URL=sqlite+aiosqlite:///./cache/cache.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - cve-network

  # 测试服务
  cve-test:
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        USER_ID: ${USER_ID:-1001}
        GROUP_ID: ${GROUP_ID:-1001}
        USERNAME: testuser
    container_name: cve-test-service
    volumes:
      # 挂载源代码（用于开发时测试）
      - .:/app
      # 挂载测试报告目录
      - ./reports:/app/reports
      - ./htmlcov:/app/htmlcov
    environment:
      - TESTING=1
      - PYTHONPATH=/app
    networks:
      - cve-network
    profiles:
      - test

networks:
  cve-network:
    driver: bridge

volumes:
  cache:
    driver: local
  logs:
    driver: local
