
在开发之前, 你需要遵循以下的前提:

- 请你在创建项目之前, 先根据要求设计一个结构规范标准的项目结构.
- 请你在开发的过程中详细的记录中文文档
- 所有的软件允许过程都要放在 docker 中, 你需要创建对应的 dockerfile 以及 docker-compose 文件(镜像推荐选用python:3.12-slim)


当前位于一个空的项目中, 你的任务如下:

- 目标的搜索目录是 “ ~/Github/cvelistV5”
- 参考我的搜索脚本 `search_files.py`
- 使用 fastapi 编写一个服务器, 设计一些格式良好的 API 能够远程查询
- 添加一个 http 单文件页面, 使得用户能够通过网页进行查询
- 请你良好的设计网页界面, 确保其现代化(使用 Vue.js 或者 Reace.js, 并且使用现代的 UI 框架). 
    - 网页主题是一个搜索框, 可以让用户输入搜索的条件, 同时, 下方有一些搜索的示例 
    - 点击搜索后能够在下方列出所有满足条件的漏洞, 漏洞信息从新到旧排列, 
    - 漏洞的详情中包含漏洞的编号, 漏洞的标题, 漏洞的描述. 以及 LLM 的总结信息
- 添加一个 LLM 总结模块, 通过 litellm 模块使用 deepseek-chat 模型总结漏洞的信息. 具体的 base_url 以及 api_key 保存在一个配置文件中
- 使用 sqlite 添加一个缓存. 主要缓存 llm 的总结信息与漏洞编号. 对于重复的搜索优先查询缓存
- 你需要不断地测试代码直到确保代码能够正常运行


----

非常好. 接下来我有如下的修改要求:
- 当前的 llm 总结模块在用户请求的时候就已经请求了, 这导致用户在请求未缓存的消息时会等待很长时间. 我希望你修改为前端请不论有没有缓存都直接返回结果. 然后前端添加一个按钮, 点一下就可以请求 llm 总结.(由于动态请求, 因此你更新缓存的时机也可能需要再次确认)
- LLM 总结的内容当前是纯文本渲染, 我希望你能够帮我支持 Markdown 格式的渲染
- 搜索框的文字有点小 帮我优化搜索框的前端展示效果
- 默认的 50 页提示不明显, 且不美观. 请你优化前端显示, 并且给几个默认的选项 分别是10 20 50 100; 默认 50
- 请你在搜索结果中显示搜索结果数目信息
- 搜索语法示例, 在得到搜索结果后, 应该就自动收起了. 直到用户点开再显示(没有搜索时还是需要提示搜索结果)


----

谢谢你, 已经很不错了. 但是还存在一些问题, 请继续帮我修改:

前端方面:
- "最大结果数"后应该是一个按钮组, 用户可以点击按钮组选择所需的结果数
- "显示/隐藏 搜索示例"按钮的位置设计并不合理 请你帮我重新设计
- "搜索结果数目" 方面, 我需要的是总的结果数, 例如符合要求的结果包含 120 条, 虽然每页只有 50 条, 但是依然需要显示 120
- 帮我添加一个翻页功能
- 前端 AI 总结的 Markdown 的渲染不正确. 请你帮我重新检查这部分的实现.

后端方面:
- 请你参考前端的修改需求, 按照具体的需求适应性的调整接口. 确保接口清晰明确

其他方面:
- 请你帮我添加一个测试用例集, 对于每一个独立的具体功能, 都添加对应的测试代码


---

非常好, 越来越接近了. 请你继续修改:

### 前端方面
- 换页的换页符需要在顶部与底部都有, 当前只有底部有
- 前端右下角悬浮一个回到顶部的按钮


### 后端方面
-  生成 AI 总结的功能似乎被你修改坏了, 当前提示是 "GET /api/summary/CVE-2025-20276 HTTP/1.1 500 Internal Server Error"


----

好 更加接近了. 我还是发现了一些问题. 

### 后端方面
- llm 总结完, 即 `GET /api/summary/CVE-2025-20276 ` 这个系类的接口, 总结完成后没有将总结内容添加到缓存. 你需要帮我更新缓存


### 前端方面
- 目前代码文件太大了. 请你帮我拆分为 html + js + css 文件的格式. 使得代码的结构更加清晰



----

我开始生气了. 你始终没有发现自己的错误. 你需要完整的检查整体的逻辑, 重点关注:
- /api/search
- /api/summary

现在的问题是, 当我使用 `/api/summary` 总结一个漏洞信息后, 它并没有被正确的缓存, 在我第二次使用 `/api/search` 搜索相同的内容时, 它还是返回了空的总结. 这使得我不得不再次总结

请你仔细地检查缓存的生成,检索,缓存,查询等等逻辑, 不要自欺欺人, 确保你真的修复了这个问题. 



----

请你仔细阅读并理解我的代码. 我需要你帮我添加一个功能.

在进行漏洞搜索时, 常常有的一个需求是, 搜索特定类型的漏洞. 例如缓存区溢出, SQL 注入等等.
我需要你为我在搜索框以外, 添加一个 Checkbox 组, 能够让用户轻松的进行一些标记, 例如:

** 漏洞类型 **
- 缓存区溢出: `+(heap|stack)_buffer_overflow, +"heap-buffer-overflow", +"stack-buffer-overflow"`
- SQL 注入: `+sql injection, +sql_injection, +SQLi`
- 任意代码执行: `+remote code execution, +remote_code_execution, +RCE`
- 任意文件读取: `+path traversal, +path_traversal, +LFI, +RFI`

** 软件类型 **
- 服务器: `+apache, +nginx, +tomcat, +iis`
- 数据库: `+mysql, +mariadb, +postgresql, +mongodb`
- 操作系统: `+windows, +linux, +macos, +unix`

** 其他 **
- 开发语言: `+php`, `+java`, `+python`, `+javascript`



等等. 我举得例子并不一定完全, 你可以根据你的理解对我的例子进行分组与修改或拓展.  

在用户点击搜索发送请求时, 你手动拼接用户的搜索内容以及 Checkbox 的内容作为查询语句进行请求. 

---

当前 前端显示的漏洞描述, 长度被截断了. 过长的内容会显示为...  请你修复此问题, 让我们能够完整的看到漏洞的描述. 

---

请你仔细理解当前项目中关于网页的部分, 帮我切换到 React.js + Material-UI . 请你充分理解前端代码的功能, 确保现有功能逻辑都正常的情况下为我进行迁移
请注意 不允许使用 nodejs 你帮我弄成 html + js + css 的模式 通过网页嵌入代码

---

1. 前端显示优化, 当前的界面中有一些字体或者按钮的渲染存在问题. 例如显示筛选器后面的数字会溢出边框, 以及很多文字是黑色的, 在视觉上看起来很不好看
2. 我需要你帮我添加一个新的接口. 对于每一个搜索的结果, 旁边添加一个按钮, 点击能够在网页中弹出一个新的悬浮"卡片"或者"内部悬浮窗口"之类的, 请求服务器获取原始的 json 信息. 背景是黑色, 并且我需要 js 能够支持折叠与代码高亮等功能. 你可能需要添加相应的 json 渲染库等. 


----

前端渲染中, 当用户点击 "查看详情" 后, json 的渲染正确. 但是当一个字段内容过长时, 会超出容器, 导致用户需要拖动横向的滚动条才能观看. 我希望你在这个子卡片中添加一个按钮, 能够切换渲染模式. 具体而言, 既能够保持现在这样渲染一个很长的单行, 也可以开启 wordWrap 让代码自动显示到下一行


----

接下来, 你要做一个非常具体的任务:
- 你需要将 LLM 请求模块添加一个流式请求的接口

前端:
- "生成AI总结"的功能需要出现打字机的效果, 请求后端的流式请求接口, 并及时渲染到前端
    - 如果命中缓存, 即一次性获取到了所有的消息, 则不需要打字机效果, 直接显示即可. 
- 后端的 LLM 总结接口需要添加一个新的, 采用 Stream=True 的请求方式, 进行请求
- 流式请求完成后, 也需要更新数据库中的缓存记录. 

----

请你充分理解当前项目的功能与结构, 然后帮我重新调整代码结构.
当前项目的结构存在很多问题, 例如:
1. 很多测试文件都放在了根目录中
2. 测试脚本覆盖的结构与功能不完全. 没有办法进行统一入口的测试
3. 很多说明的文档放在了项目根目录下, 导致看着很混乱
4. 请确保代码的核心功能逻辑不变, 测试脚本需要尽可能的覆盖所有的接口函数
5. 根据你对代码的理解, 重新写一份 README 文档

----

请检查 Docker 的配置, 并且在 Docker 环境中运行所有的测试用例. 确保所有的测试都能够正常通过. 
请注意, 当前代码中的 Docker 相关文件完全没有经过测试. 你可以完全重写相关的配置. 
请确保 Docker 运行的用户与当前用户的 uid 与 gid 相同. 


----

接下来， 我需要将前端迁移到 NodeJS 环境。 请你仔细阅读当前的代码， 并将其迁移到 NodeJS 环境。 
具体而言， 我需要使用 FastAPI 同时管理前后端， 项目的结构大致如下(请注意, 这是一个参考的项目结构, 具体的结构需要由你进行调整与修改. )：
```
my-fullstack-app/
├── backend/                  # FastAPI 后端项目
│   ├── app/
│   │   └── main.py           # FastAPI 主入口
│   ├── requirements.txt
│   └── Dockerfile            # 后端 Dockerfile (包含前端构建产物)
│
├── frontend/                 # React 前端项目
│   ├── public/
│   ├── src/
│   ├── package.json
│   ├── tsconfig.json         # (如果使用 TypeScript)
│   └── .env.production       # 生产环境变量 (例如 API 基础 URL)
│
├── .gitignore
├── docker-compose.yml        # (可选) 本地开发环境编排
```
前端的代码编译时, 需要将相应的静态资源拷贝/链接到 backend 中. 确保 FastAPI 能够正确的访问资源;

同时, 你需要统一后端的接口格式, 确保采用 /api/v1/ 开头
在你完成工具迁移后, 请确保所有的测试套件能够正常运行. 

现有的前端逻辑需要你合理的进行拆分, 确保各个子模块划分明确, 逻辑清晰, 便于维护. 


----

前端渲染的逻辑依然存在问题:
当前端点击 "生成总结" 按钮后, 打字机效果的 LLM 响应渲染功能正确. 但是当请求完成后, 内容被渲染为 `[object Object]`, 且再次搜索对应的内容时, 命中缓存得到的内容依然是 `[object Object]`. 我已经通过抓包确定了后台响应的内容是正确的, 结构如下:
```
{
    "query": "python",
    "total_results": 1455,
    "page": 1,
    "page_size": 20,
    "total_pages": 73,
    "results": [
        {
            "cve_info": {
                "cve_id": "CVE-2025-49142",
                "title": "Nautobot vulnerable to secrets exposure and data manipulation through Jinja2 templating",
                "description": "Nautobot is a Network Source of Truth and Network Automation Platform. All users of Nautobot versions prior to 2.4.10 or prior to 1.6.32 are potentially affected. Due to insufficient security configuration of the Jinja2 templating feature used in computed fields, custom links, etc. in Nautobot, a malicious user could configure this feature set in ways that could expose the value of Secrets defined in Nautobot when the templated content is rendered or that could call Python APIs to modify data within Nautobot when the templated content is rendered, bypassing the object permissions assigned to the viewing user. Nautobot versions 1.6.32 and 2.4.10 will include fixes for the vulnerability. The vulnerability can be partially mitigated by configuring object permissions appropriately to limit certain actions to only trusted users.",
                "published_date": "2025-06-10T15:40:21.105Z",
                "modified_date": "2025-06-10T17:10:21.784Z",
                "severity": null,
                "cvss_score": null,
                "affected_products": [
                    "nautobot nautobot"
                ],
                "references": [
                    "https://github.com/nautobot/nautobot/security/advisories/GHSA-wjw6-95h5-4jpx",
                    "https://github.com/nautobot/nautobot/pull/7417",
                    "https://github.com/nautobot/nautobot/pull/7429",
                    "https://docs.djangoproject.com/en/4.2/ref/templates/api/#alters-data-description",
                    "https://jinja.palletsprojects.com/en/stable/sandbox"
                ],
                "file_path": "/home/<USER>/Github/cvelistV5/cves/2025/49xxx/CVE-2025-49142.json"
            },
            "summary": {
                "cve_id": "CVE-2025-49142",
                "summary": "### 中文翻译：\nNautobot是一个网络真实源和网络自动化平台。所有使用2.4.10之前版本或1.6.32之前版本的Nautobot用户都可能受到影响。由于Nautobot中用于计算字段、自定义链接等的Jinja2模板功能安全配置不足，恶意用户可以通过配置该功能集在模板内容渲染时暴露Nautobot中定义的Secrets值，或调用Python API修改Nautobot中的数据，从而绕过分配给查看用户的对象权限。Nautobot 1.6.32和2.4.10版本将包含针对该漏洞的修复。通过适当配置对象权限以限制某些操作仅限受信任用户，可以部分缓解该漏洞。\n\n### 漏洞关键信息总结：\n\n1. **受影响系统/软件基础信息**  \n   - **软件**：Nautobot（网络真实源和网络自动化平台）  \n   - **功能**：用于网络设备管理、自动化及数据源验证。  \n   - **影响版本**：1.6.32之前的所有版本、2.4.10之前的所有版本。  \n   - **影响范围**：所有未升级的用户，尤其是依赖敏感数据（如Secrets）和权限控制的场景。  \n\n2. **漏洞本质及影响**  \n   - **本质**：Jinja2模板功能的安全配置缺陷，导致权限绕过和敏感数据泄露。  \n   - **影响**：  \n     - 恶意用户可通过模板渲染暴露敏感信息（如Secrets）。  \n     - 通过API调用篡改数据，绕过对象权限限制。  \n\n3. **潜在安全风险**  \n   - **数据泄露**：Secrets等敏感信息可能被未授权访问。  \n   - **数据篡改**：关键网络配置可能被恶意修改，导致服务中断或安全漏洞。  \n   - **权限绕过**：低权限用户可能执行高权限操作。  \n\n4. **防护措施**  \n   - **升级修复**：立即升级至1.6.32或2.4.10版本。  \n   - **权限管控**：严格限制模板配置权限，仅允许受信任用户操作。  \n   - **监控审计**：检查日志中异常模板渲染或数据修改行为。  \n\n**总结**：该漏洞威胁性较高，需优先升级并加强权限管理，防止数据泄露和未授权操作。",
                "generated_at": "2025-06-11T10:17:35.366623",
                "model_used": "openrouter/deepseek/deepseek-chat-v3-0324:free"
            },
            "raw_data": null
        },
        {
            "cve_info": {
                "cve_id": "CVE-2025-49131",
                <以下内容省略>
```

