{"name": "jest-environment-jsdom", "version": "30.0.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment-jsdom"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "30.0.0", "@jest/environment-jsdom-abstract": "30.0.0", "@types/jsdom": "^21.1.7", "@types/node": "*", "jsdom": "^26.1.0"}, "devDependencies": {"@jest/test-utils": "30.0.0"}, "peerDependencies": {"canvas": "^3.0.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418"}