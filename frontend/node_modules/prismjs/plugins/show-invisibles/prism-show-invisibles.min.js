!function(){if("undefined"!=typeof Prism){var r={tab:/\t/,crlf:/\r\n/,lf:/\n/,cr:/\r/,space:/ /};Prism.hooks.add("before-highlight",(function(r){i(r.grammar)}))}function e(r,a){var n=r[a];switch(Prism.util.type(n)){case"RegExp":var t={};r[a]={pattern:n,inside:t},i(t);break;case"Array":for(var f=0,s=n.length;f<s;f++)e(n,f);break;default:i(t=n.inside||(n.inside={}))}}function i(a){if(a&&!a.tab){for(var n in r)r.hasOwnProperty(n)&&(a[n]=r[n]);for(var n in a)a.hasOwnProperty(n)&&!r[n]&&("rest"===n?i(a.rest):e(a,n))}}}();