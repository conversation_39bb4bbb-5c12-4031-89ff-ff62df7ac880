import { useState, useEffect, useRef } from 'react';

interface UseTypewriterProps {
  text: string;
  speed?: number;
}

export const useTypewriter = ({ text, speed = 20 }: UseTypewriterProps) => {
  const [displayText, setDisplayText] = useState('');
  const textRef = useRef('');
  const indexRef = useRef(0);
  const timerRef = useRef<number | null>(null);

  // 当text变化时，更新目标文本但不重置显示
  useEffect(() => {
    textRef.current = text;

    // 如果新文本比当前显示的文本长，继续打字机效果
    if (text.length > displayText.length) {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      const typeNextChar = () => {
        if (indexRef.current < textRef.current.length) {
          setDisplayText(textRef.current.substring(0, indexRef.current + 1));
          indexRef.current++;
          timerRef.current = setTimeout(typeNextChar, speed);
        }
      };

      // 如果当前没有在打字，开始打字
      if (indexRef.current <= displayText.length) {
        indexRef.current = displayText.length;
        typeNextChar();
      }
    }
  }, [text, speed, displayText.length]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return displayText;
};
