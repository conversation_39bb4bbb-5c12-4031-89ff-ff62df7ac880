import { SearchResponse, CVEDetail, SearchExample } from '../types';

const API_BASE = '/api/v1';

class ApiService {
  // 搜索 CVE
  async searchCVEs(
    query: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<SearchResponse> {
    const params = new URLSearchParams({
      query,
      page: page.toString(),
      page_size: pageSize.toString()
    });

    const response = await fetch(`${API_BASE}/search?${params}`);
    
    if (!response.ok) {
      throw new Error(`搜索失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取特定 CVE 详情
  async getCVEDetail(cveId: string): Promise<CVEDetail> {
    const response = await fetch(`${API_BASE}/cve/${cveId}`);
    
    if (!response.ok) {
      throw new Error(`获取 CVE 详情失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取 CVE 总结
  async getCVESummary(cveId: string, forceRefresh: boolean = false): Promise<any> {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('force_refresh', 'true');
    }

    const response = await fetch(`${API_BASE}/summary/${cveId}?${params}`);
    
    if (!response.ok) {
      throw new Error(`获取总结失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 流式获取 CVE 总结
  async getCVESummaryStream(
    cveId: string,
    forceRefresh: boolean = false
  ): Promise<ReadableStream<Uint8Array> | null> {
    const params = new URLSearchParams();
    if (forceRefresh) {
      params.append('force_refresh', 'true');
    }

    const response = await fetch(`${API_BASE}/summary/${cveId}/stream?${params}`);
    
    if (!response.ok) {
      throw new Error(`流式获取总结失败: ${response.statusText}`);
    }

    return response.body;
  }

  // 获取 CVE 原始数据
  async getCVERawData(cveId: string): Promise<any> {
    const response = await fetch(`${API_BASE}/cve/${cveId}/raw`);
    
    if (!response.ok) {
      throw new Error(`获取原始数据失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取搜索示例
  async getSearchExamples(): Promise<{ examples: SearchExample[] }> {
    const response = await fetch(`${API_BASE}/examples`);
    
    if (!response.ok) {
      throw new Error(`获取搜索示例失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 健康检查
  async healthCheck(): Promise<any> {
    const response = await fetch(`${API_BASE}/health`);
    
    if (!response.ok) {
      throw new Error(`健康检查失败: ${response.statusText}`);
    }

    return response.json();
  }
}

export const apiService = new ApiService();
