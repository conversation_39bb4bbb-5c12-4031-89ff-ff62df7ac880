import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CVESearchApp } from '../components/CVESearchApp';
import { apiService } from '../services/api';

// Mock the API service
jest.mock('../services/api');
const mockApiService = apiService as jest.Mocked<typeof apiService>;

// Mock renderMarkdown utility
jest.mock('../utils', () => ({
  renderMarkdown: (text: string) => text,
  formatDate: (date: string) => date,
  truncateText: (text: string, length: number) => text.substring(0, length),
  getSeverityColor: () => 'primary'
}));

describe('Streaming Concurrency Tests', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    mockApiService.searchCVEs.mockClear();
    mockApiService.getCVESummaryStream.mockClear();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const mockSearchResults = {
    query: 'test query',
    results: [
      {
        cve_info: {
          cve_id: 'CVE-2023-0001',
          title: 'Test CVE 1',
          description: 'Test description 1',
          published_date: '2023-01-01',
          last_modified_date: '2023-01-01',
          cvss_score: 7.5,
          cvss_severity: 'HIGH',
          cwe_ids: ['CWE-79'],
          affected_products: ['Product 1'],
          references: ['https://example.com/1'],
          file_path: '/path/to/cve1.json'
        },
        summary: undefined
      },
      {
        cve_info: {
          cve_id: 'CVE-2023-0002',
          title: 'Test CVE 2',
          description: 'Test description 2',
          published_date: '2023-01-02',
          last_modified_date: '2023-01-02',
          cvss_score: 8.0,
          cvss_severity: 'HIGH',
          cwe_ids: ['CWE-89'],
          affected_products: ['Product 2'],
          references: ['https://example.com/2'],
          file_path: '/path/to/cve2.json'
        },
        summary: undefined
      }
    ],
    total_results: 2,
    page: 1,
    page_size: 20,
    total_pages: 1,
    search_time: 0.1
  };

  const createMockStream = (chunks: string[], delay: number = 50) => {
    let index = 0;
    const encoder = new TextEncoder();
    
    return new ReadableStream({
      start(controller) {
        const sendChunk = () => {
          if (index < chunks.length) {
            controller.enqueue(encoder.encode(chunks[index]));
            index++;
            setTimeout(sendChunk, delay);
          } else {
            controller.close();
          }
        };
        sendChunk();
      }
    });
  };

  test('should handle concurrent streaming requests without interference', async () => {
    // Mock search results
    mockApiService.searchCVEs.mockResolvedValue(mockSearchResults);

    // Mock streaming responses for both CVEs
    const stream1Chunks = [
      'data: {"type": "chunk", "data": "This is "}\n',
      'data: {"type": "chunk", "data": "the first "}\n',
      'data: {"type": "chunk", "data": "CVE summary"}\n',
      'data: {"type": "complete", "data": "This is the first CVE summary"}\n'
    ];

    const stream2Chunks = [
      'data: {"type": "chunk", "data": "This is "}\n',
      'data: {"type": "chunk", "data": "the second "}\n',
      'data: {"type": "chunk", "data": "CVE summary"}\n',
      'data: {"type": "complete", "data": "This is the second CVE summary"}\n'
    ];

    mockApiService.getCVESummaryStream
      .mockResolvedValueOnce(createMockStream(stream1Chunks))
      .mockResolvedValueOnce(createMockStream(stream2Chunks));

    render(<CVESearchApp />);

    // Perform search
    const searchInput = screen.getByPlaceholderText(/输入搜索条件/);
    const searchButton = screen.getByText('搜索');

    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('CVE-2023-0001')).toBeInTheDocument();
      expect(screen.getByText('CVE-2023-0002')).toBeInTheDocument();
    });

    // Start both streaming requests simultaneously
    const generateButtons = screen.getAllByText('生成总结');
    expect(generateButtons).toHaveLength(2);

    fireEvent.click(generateButtons[0]);
    fireEvent.click(generateButtons[1]);

    // Fast-forward time to complete streaming
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('This is the first CVE summary')).toBeInTheDocument();
      expect(screen.getByText('This is the second CVE summary')).toBeInTheDocument();
    });

    // Verify both API calls were made
    expect(mockApiService.getCVESummaryStream).toHaveBeenCalledTimes(2);
    expect(mockApiService.getCVESummaryStream).toHaveBeenCalledWith('CVE-2023-0001');
    expect(mockApiService.getCVESummaryStream).toHaveBeenCalledWith('CVE-2023-0002');
  });

  test('should cancel previous request when new one is started', async () => {
    mockApiService.searchCVEs.mockResolvedValue({
      ...mockSearchResults,
      results: [mockSearchResults.results[0]] // Only one CVE
    });

    const firstStreamChunks = [
      'data: {"type": "chunk", "data": "First request "}\n',
      'data: {"type": "chunk", "data": "in progress..."}\n'
    ];

    const secondStreamChunks = [
      'data: {"type": "chunk", "data": "Second request "}\n',
      'data: {"type": "chunk", "data": "completed"}\n',
      'data: {"type": "complete", "data": "Second request completed"}\n'
    ];

    mockApiService.getCVESummaryStream
      .mockResolvedValueOnce(createMockStream(firstStreamChunks, 200))
      .mockResolvedValueOnce(createMockStream(secondStreamChunks, 50));

    render(<CVESearchApp />);

    // Perform search
    const searchInput = screen.getByPlaceholderText(/输入搜索条件/);
    const searchButton = screen.getByText('搜索');

    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('CVE-2023-0001')).toBeInTheDocument();
    });

    // Start first request
    const generateButton = screen.getByText('生成总结');
    fireEvent.click(generateButton);

    // Wait a bit for first request to start
    jest.advanceTimersByTime(100);

    // Start second request (should cancel first)
    const refreshButton = screen.getByText('刷新');
    fireEvent.click(refreshButton);

    // Complete all timers
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('Second request completed')).toBeInTheDocument();
    });

    // Should not show content from first request
    expect(screen.queryByText('First request in progress...')).not.toBeInTheDocument();
  });

  test('should handle cached responses correctly', async () => {
    mockApiService.searchCVEs.mockResolvedValue({
      ...mockSearchResults,
      results: [mockSearchResults.results[0]]
    });

    const cachedStreamChunks = [
      'data: {"type": "cache", "data": "Cached summary content"}\n'
    ];

    mockApiService.getCVESummaryStream.mockResolvedValue(
      createMockStream(cachedStreamChunks)
    );

    render(<CVESearchApp />);

    // Perform search
    const searchInput = screen.getByPlaceholderText(/输入搜索条件/);
    const searchButton = screen.getByText('搜索');

    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('CVE-2023-0001')).toBeInTheDocument();
    });

    // Start streaming request
    const generateButton = screen.getByText('生成总结');
    fireEvent.click(generateButton);

    jest.advanceTimersByTime(200);

    await waitFor(() => {
      expect(screen.getByText('Cached summary content')).toBeInTheDocument();
    });

    // Should show cache notification
    expect(screen.getByText('从缓存获取 AI 总结')).toBeInTheDocument();
  });
});
