import { render, screen, waitFor } from '@testing-library/react';
import { TypewriterText } from '../components/TypewriterText';

// Mock renderMarkdown utility
jest.mock('../utils', () => ({
  renderMarkdown: (text: string) => text
}));

describe('TypewriterText Concurrency Tests', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  test('should handle multiple instances independently', async () => {
    const { rerender } = render(
      <div>
        <TypewriterText 
          text="First text" 
          speed={10} 
          instanceId="instance-1"
        />
        <TypewriterText 
          text="Second text" 
          speed={10} 
          instanceId="instance-2"
        />
      </div>
    );

    // Fast-forward time to complete typing
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('First text')).toBeInTheDocument();
      expect(screen.getByText('Second text')).toBeInTheDocument();
    });

    // Update both texts simultaneously
    rerender(
      <div>
        <TypewriterText 
          text="Updated first text" 
          speed={10} 
          instanceId="instance-1"
        />
        <TypewriterText 
          text="Updated second text" 
          speed={10} 
          instanceId="instance-2"
        />
      </div>
    );

    // Fast-forward time to complete typing
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('Updated first text')).toBeInTheDocument();
      expect(screen.getByText('Updated second text')).toBeInTheDocument();
    });
  });

  test('should handle rapid text updates without interference', async () => {
    const { rerender } = render(
      <TypewriterText 
        text="Initial text" 
        speed={10} 
        instanceId="rapid-test"
      />
    );

    // Rapidly update text multiple times
    const updates = [
      'First update',
      'Second update',
      'Third update',
      'Final update'
    ];

    for (const update of updates) {
      rerender(
        <TypewriterText 
          text={update} 
          speed={10} 
          instanceId="rapid-test"
        />
      );
      jest.advanceTimersByTime(50); // Small delay between updates
    }

    // Complete the typing animation
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('Final update')).toBeInTheDocument();
    });
  });

  test('should reset properly when text becomes shorter', async () => {
    const { rerender } = render(
      <TypewriterText 
        text="This is a very long text that will be shortened" 
        speed={10} 
        instanceId="length-test"
      />
    );

    // Complete initial typing
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('This is a very long text that will be shortened')).toBeInTheDocument();
    });

    // Update to shorter text
    rerender(
      <TypewriterText 
        text="Short text" 
        speed={10} 
        instanceId="length-test"
      />
    );

    // Complete new typing
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('Short text')).toBeInTheDocument();
    });
  });

  test('should handle empty text gracefully', async () => {
    const { rerender } = render(
      <TypewriterText 
        text="Some text" 
        speed={10} 
        instanceId="empty-test"
      />
    );

    // Complete initial typing
    jest.advanceTimersByTime(1000);

    // Update to empty text
    rerender(
      <TypewriterText 
        text="" 
        speed={10} 
        instanceId="empty-test"
      />
    );

    jest.advanceTimersByTime(100);

    // Should handle empty text without errors
    expect(screen.queryByText('Some text')).not.toBeInTheDocument();
  });

  test('should generate unique instance IDs when not provided', () => {
    const { container: container1 } = render(
      <TypewriterText text="Text 1" speed={10} />
    );
    
    const { container: container2 } = render(
      <TypewriterText text="Text 2" speed={10} />
    );

    // Both should render without conflicts
    expect(container1).toBeInTheDocument();
    expect(container2).toBeInTheDocument();
  });
});
