#!/usr/bin/env python3
"""
测试迁移是否成功的简单脚本
"""

import sys
import os
import requests
import time
import subprocess
from pathlib import Path

def test_backend_import():
    """测试后端导入"""
    print("🔍 测试后端模块导入...")
    try:
        sys.path.insert(0, str(Path(__file__).parent / 'backend'))
        from app.main import app
        print("✅ 后端模块导入成功")
        
        # 检查路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print(f"📋 发现 {len(routes)} 个路由:")
        for route in sorted(routes):
            print(f"   {route}")
        
        return True
    except Exception as e:
        print(f"❌ 后端模块导入失败: {e}")
        return False

def test_frontend_build():
    """测试前端构建产物"""
    print("\n🔍 测试前端构建产物...")
    static_dir = Path(__file__).parent / 'backend' / 'app' / 'static'
    
    if not static_dir.exists():
        print("❌ 静态文件目录不存在")
        return False
    
    required_files = ['index.html']
    missing_files = []
    
    for file in required_files:
        if not (static_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 前端构建产物检查通过")
    return True

def test_docker_build():
    """测试 Docker 构建"""
    print("\n🔍 测试 Docker 构建...")
    try:
        result = subprocess.run([
            'docker', 'images', 'cve-search-fullstack'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and 'cve-search-fullstack' in result.stdout:
            print("✅ Docker 镜像构建成功")
            return True
        else:
            print("❌ Docker 镜像不存在")
            return False
    except Exception as e:
        print(f"❌ Docker 检查失败: {e}")
        return False

def test_api_endpoints():
    """测试 API 端点（如果服务正在运行）"""
    print("\n🔍 测试 API 端点...")
    base_url = "http://localhost:8001"
    
    endpoints = [
        "/api/v1/health",
        "/api/v1/examples",
        "/",
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code in [200, 404]:  # 404 也可以接受，说明服务在运行
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
            else:
                print(f"⚠️  {endpoint} - 状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  {endpoint} - 服务未运行")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")

def main():
    """主测试函数"""
    print("🚀 CVE 搜索服务 - Node.js 前端迁移验证")
    print("=" * 50)
    
    results = []
    
    # 测试后端导入
    results.append(test_backend_import())
    
    # 测试前端构建
    results.append(test_frontend_build())
    
    # 测试 Docker 构建
    results.append(test_docker_build())
    
    # 测试 API 端点
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 迁移验证结果:")
    print(f"✅ 成功: {sum(results)}/{len(results)} 项测试通过")
    
    if all(results):
        print("🎉 迁移验证完全成功！")
        return 0
    else:
        print("⚠️  部分测试未通过，请检查相关配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
