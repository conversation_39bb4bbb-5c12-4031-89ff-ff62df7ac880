#!/usr/bin/env python3
"""
测试搜索功能修复的脚本
验证 API 路径修复和数据结构防护是否有效
"""

import requests
import json
import time

def test_api_endpoints():
    """测试各个 API 端点"""
    base_url = "http://localhost:8001"
    
    print("🔍 测试 CVE 搜索 API 修复")
    print("=" * 50)
    
    # 测试健康检查
    print("1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查成功: {data['status']}")
            print(f"   📁 CVE 目录存在: {data['cve_directory_exists']}")
            print(f"   🗄️ 数据库连接: {data['database_connected']}")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    print()
    
    # 测试搜索 API
    print("2. 测试搜索 API...")
    try:
        response = requests.get(
            f"{base_url}/api/v1/search",
            params={"query": "test", "page": 1, "page_size": 3},
            timeout=15
        )
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 搜索成功")
            print(f"   📊 总结果数: {data.get('total_results', 0)}")
            print(f"   📄 当前页: {data.get('page', 0)}")
            print(f"   📋 结果数组长度: {len(data.get('results', []))}")
            print(f"   ⏱️ 搜索耗时: {data.get('search_time', 0):.3f}s")
            
            # 验证数据结构
            if 'results' in data and isinstance(data['results'], list):
                print("   ✅ 数据结构正确: results 是数组")
                if data['results']:
                    first_result = data['results'][0]
                    if 'cve_info' in first_result:
                        print("   ✅ CVE 信息结构正确")
                        print(f"   🆔 第一个 CVE ID: {first_result['cve_info'].get('cve_id', 'N/A')}")
                    else:
                        print("   ⚠️ CVE 信息结构缺失")
                else:
                    print("   ℹ️ 结果数组为空")
            else:
                print("   ❌ 数据结构错误: results 不是数组")
        else:
            print(f"   ❌ 搜索失败: {response.status_code}")
            print(f"   📝 响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ 搜索异常: {e}")
    
    print()
    
    # 测试搜索示例 API
    print("3. 测试搜索示例 API...")
    try:
        response = requests.get(f"{base_url}/api/v1/examples", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取示例成功")
            print(f"   📚 示例数量: {len(data.get('examples', []))}")
            if data.get('examples'):
                first_example = data['examples'][0]
                print(f"   📝 第一个示例: {first_example.get('title', 'N/A')}")
        else:
            print(f"   ❌ 获取示例失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 获取示例异常: {e}")
    
    print()
    
    # 测试边缘情况
    print("4. 测试边缘情况...")
    
    # 测试空查询
    try:
        response = requests.get(
            f"{base_url}/api/v1/search",
            params={"query": "", "page": 1, "page_size": 5},
            timeout=10
        )
        if response.status_code == 422:
            print("   ✅ 空查询正确返回 422 错误")
        else:
            print(f"   ⚠️ 空查询返回状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 空查询测试异常: {e}")
    
    # 测试不存在的 CVE
    try:
        response = requests.get(
            f"{base_url}/api/v1/search",
            params={"query": "CVE-9999-99999", "page": 1, "page_size": 5},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('total_results', 0) == 0:
                print("   ✅ 不存在的 CVE 正确返回空结果")
            else:
                print(f"   ⚠️ 不存在的 CVE 返回了 {data.get('total_results', 0)} 个结果")
        else:
            print(f"   ❌ 不存在的 CVE 查询失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 不存在的 CVE 测试异常: {e}")
    
    print()
    print("🎯 测试完成!")
    print("=" * 50)
    print("💡 如果所有测试都通过，说明 API 路径修复和数据结构防护已生效")
    print("🌐 请在浏览器中访问 http://localhost:8001 测试前端功能")

if __name__ == "__main__":
    test_api_endpoints()
