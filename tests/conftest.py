"""
测试配置文件
提供测试所需的共享 fixtures 和配置
"""

import pytest
import asyncio
import tempfile
import os
import shutil
from pathlib import Path
from typing import AsyncGenerator, Generator
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.main import app
from app.core.database import Base, get_database
from app.core.config import settings
from app.core.search_engine import CVESearchEngine
from app.services.cache_service import CacheService
from app.services.llm_service import LLMService


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_db_engine():
    """创建测试数据库引擎"""
    # 使用内存数据库进行测试
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False
    )

    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async_session = async_sessionmaker(
        test_db_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session


@pytest.fixture
async def test_db():
    """创建测试数据库（保持向后兼容）"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db')
    os.close(db_fd)

    # 创建测试引擎
    test_engine = create_async_engine(
        f"sqlite+aiosqlite:///{db_path}",
        echo=False
    )

    # 创建表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话工厂
    TestSessionLocal = async_sessionmaker(
        test_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )

    yield TestSessionLocal

    # 清理
    await test_engine.dispose()
    os.unlink(db_path)


@pytest.fixture(scope="session")
def test_cve_directory() -> Generator[str, None, None]:
    """创建测试 CVE 目录"""
    temp_dir = tempfile.mkdtemp()

    # 创建测试 CVE 文件
    test_cve_dir = Path(temp_dir) / "cves" / "2023"
    test_cve_dir.mkdir(parents=True, exist_ok=True)

    # 创建示例 CVE 文件
    test_cve_content = {
        "cveMetadata": {
            "cveId": "CVE-2023-12345",
            "datePublished": "2023-01-01T00:00:00.000Z",
            "dateUpdated": "2023-01-02T00:00:00.000Z"
        },
        "containers": {
            "cna": {
                "title": "Test Buffer Overflow Vulnerability",
                "descriptions": [
                    {
                        "lang": "en",
                        "value": "A buffer overflow vulnerability exists in test application that allows remote code execution."
                    }
                ],
                "affected": [
                    {
                        "vendor": "TestVendor",
                        "product": "TestProduct",
                        "versions": [
                            {
                                "version": "1.0.0",
                                "status": "affected"
                            }
                        ]
                    }
                ],
                "references": [
                    {
                        "url": "https://example.com/advisory",
                        "name": "Vendor Advisory"
                    }
                ],
                "metrics": [
                    {
                        "cvssV3_1": {
                            "version": "3.1",
                            "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
                            "baseScore": 9.8,
                            "baseSeverity": "CRITICAL"
                        }
                    }
                ]
            }
        }
    }

    import json
    with open(test_cve_dir / "CVE-2023-12345.json", "w") as f:
        json.dump(test_cve_content, f, indent=2)

    yield str(temp_dir)

    # 清理
    shutil.rmtree(temp_dir)


@pytest.fixture
def test_cve_data():
    """创建测试 CVE 数据目录（保持向后兼容）"""
    with tempfile.TemporaryDirectory() as temp_dir:
        cve_dir = Path(temp_dir) / "cves" / "2023"
        cve_dir.mkdir(parents=True)

        # 创建测试 CVE 文件
        test_cve = {
            "cveMetadata": {
                "cveId": "CVE-2023-TEST",
                "datePublished": "2023-01-01T00:00:00.000Z",
                "dateUpdated": "2023-01-02T00:00:00.000Z"
            },
            "containers": {
                "cna": {
                    "descriptions": [
                        {
                            "lang": "en",
                            "value": "Test vulnerability description"
                        }
                    ],
                    "affected": [
                        {
                            "vendor": "Test Vendor",
                            "product": "Test Product"
                        }
                    ],
                    "metrics": [
                        {
                            "cvssV3_1": {
                                "baseScore": 7.5,
                                "baseSeverity": "HIGH"
                            }
                        }
                    ]
                }
            }
        }

        import json
        with open(cve_dir / "CVE-2023-TEST.json", "w") as f:
            json.dump(test_cve, f)

        yield temp_dir


@pytest.fixture
def test_client(test_db_session):
    """创建测试客户端"""
    def override_get_database():
        return test_db_session

    app.dependency_overrides[get_database] = override_get_database

    with TestClient(app) as client:
        yield client

    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture
def test_search_engine(test_cve_directory):
    """创建测试搜索引擎"""
    return CVESearchEngine(test_cve_directory)


@pytest.fixture
def test_cache_service():
    """创建测试缓存服务"""
    return CacheService()


@pytest.fixture
def test_llm_service():
    """创建测试 LLM 服务"""
    return LLMService()


@pytest.fixture
def mock_llm_response():
    """模拟 LLM 响应"""
    return {
        "summary": "这是一个严重的缓冲区溢出漏洞，影响 TestProduct 1.0.0 版本。攻击者可以通过网络远程利用此漏洞执行任意代码。建议立即升级到最新版本。",
        "model_used": "deepseek-chat",
        "generated_at": "2023-01-01T00:00:00.000Z"
    }


@pytest.fixture
def client(test_db, test_cve_data):
    """创建测试客户端（保持向后兼容）"""
    async def override_get_database():
        async with test_db() as session:
            yield session

    # 覆盖依赖
    app.dependency_overrides[get_database] = override_get_database

    # 临时修改 CVE 目录设置
    original_cve_dir = settings.search.cve_directory
    settings.search.cve_directory = test_cve_data

    with TestClient(app) as test_client:
        yield test_client

    # 恢复设置
    settings.search.cve_directory = original_cve_dir
    app.dependency_overrides.clear()


# 测试标记
pytest_plugins = []

# 测试配置
def pytest_configure(config):
    """配置测试环境"""
    # 设置测试环境变量
    os.environ["TESTING"] = "1"

    # 注册自定义标记
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为不同目录的测试添加标记
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
