"""
API 集成测试
测试完整的 API 工作流程
"""

import pytest
import json
import time
from unittest.mock import patch, AsyncMock

from app.models.schemas import CVESummary


@pytest.mark.integration
class TestAPIIntegration:
    """API 集成测试类"""
    
    def test_health_check(self, test_client):
        """测试健康检查端点"""
        response = test_client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "cve_directory_exists" in data
        assert "database_connected" in data
    
    def test_search_workflow(self, test_client, test_cve_directory):
        """测试完整搜索工作流程"""
        # 1. 执行搜索
        response = test_client.get("/api/v1/search?query=buffer+overflow&page=1&page_size=10")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "results" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        
        # 验证结果结构
        if data["results"]:
            result = data["results"][0]
            assert "cve_info" in result
            assert "cve_id" in result["cve_info"]
    
    def test_cve_detail_workflow(self, test_client):
        """测试 CVE 详情获取工作流程"""
        # 1. 先搜索获取一个 CVE ID
        search_response = test_client.get("/api/v1/search?query=CVE-2023&page=1&page_size=1")
        assert search_response.status_code == 200
        
        search_data = search_response.json()
        if not search_data["results"]:
            pytest.skip("没有搜索结果，跳过详情测试")
        
        cve_id = search_data["results"][0]["cve_info"]["cve_id"]
        
        # 2. 获取 CVE 详情
        detail_response = test_client.get(f"/api/v1/cve/{cve_id}")
        assert detail_response.status_code == 200
        
        detail_data = detail_response.json()
        assert "cve_info" in detail_data
        assert detail_data["cve_info"]["cve_id"] == cve_id
    
    @patch('app.services.llm_service.LLMService.generate_summary')
    def test_summary_workflow(self, mock_generate_summary, test_client):
        """测试 LLM 总结工作流程"""
        # 模拟 LLM 响应
        mock_summary = CVESummary(
            cve_id="CVE-2023-12345",
            summary="这是一个测试总结",
            generated_at="2023-01-01T00:00:00.000Z",
            model_used="deepseek-chat"
        )
        mock_generate_summary.return_value = mock_summary
        
        # 1. 先搜索获取一个 CVE ID
        search_response = test_client.get("/api/v1/search?query=CVE&page=1&page_size=1")
        assert search_response.status_code == 200
        
        search_data = search_response.json()
        if not search_data["results"]:
            pytest.skip("没有搜索结果，跳过总结测试")
        
        cve_id = search_data["results"][0]["cve_info"]["cve_id"]
        
        # 2. 生成总结
        summary_response = test_client.get(f"/api/v1/summary/{cve_id}")
        assert summary_response.status_code == 200
        
        summary_data = summary_response.json()
        assert "summary" in summary_data
        assert "cve_id" in summary_data
        assert summary_data["cve_id"] == cve_id
    
    def test_search_examples(self, test_client):
        """测试搜索示例端点"""
        response = test_client.get("/api/v1/v1/examples")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "examples" in data
        assert isinstance(data["examples"], list)
        
        if data["examples"]:
            example = data["examples"][0]
            assert "title" in example
            assert "query" in example
            assert "description" in example
    
    def test_error_handling(self, test_client):
        """测试错误处理"""
        # 测试不存在的 CVE
        response = test_client.get("/api/v1/cve/CVE-9999-NONEXISTENT")
        assert response.status_code == 404
        
        error_data = response.json()
        assert "detail" in error_data
    
    def test_pagination(self, test_client):
        """测试分页功能"""
        # 测试第一页
        response1 = test_client.get("/api/v1/search?query=CVE&page=1&page_size=5")
        assert response1.status_code == 200
        
        data1 = response1.json()
        assert data1["page"] == 1
        assert data1["page_size"] == 5
        
        # 如果有足够的结果，测试第二页
        if data1["total"] > 5:
            response2 = test_client.get("/api/v1/search?query=CVE&page=2&page_size=5")
            assert response2.status_code == 200
            
            data2 = response2.json()
            assert data2["page"] == 2
            assert data2["page_size"] == 5
    
    def test_search_filters(self, test_client):
        """测试搜索过滤功能"""
        # 测试基本搜索
        response = test_client.get("/api/v1/search?query=buffer+overflow")
        assert response.status_code == 200
        
        # 测试引号搜索
        response = test_client.get('/api/v1/search?query="remote code execution"')
        assert response.status_code == 200
        
        # 测试排除搜索
        response = test_client.get("/api/v1/search?query=vulnerability+-denial")
        assert response.status_code == 200
    
    @patch('app.services.llm_service.LLMService.generate_summary_stream')
    def test_streaming_summary(self, mock_stream, test_client):
        """测试流式总结功能"""
        # 模拟流式响应
        async def mock_stream_generator():
            chunks = ["这是", "一个", "测试", "总结"]
            for chunk in chunks:
                yield chunk
        
        mock_stream.return_value = mock_stream_generator()
        
        # 先搜索获取一个 CVE ID
        search_response = test_client.get("/api/v1/search?query=CVE&page=1&page_size=1")
        assert search_response.status_code == 200
        
        search_data = search_response.json()
        if not search_data["results"]:
            pytest.skip("没有搜索结果，跳过流式测试")
        
        cve_id = search_data["results"][0]["cve_info"]["cve_id"]
        
        # 测试流式总结
        response = test_client.get(f"/api/v1/summary/{cve_id}/stream")
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/plain; charset=utf-8"
    
    def test_cache_consistency(self, test_client):
        """测试缓存一致性"""
        # 1. 执行搜索
        search_response = test_client.get("/api/v1/search?query=buffer&page=1&page_size=1")
        assert search_response.status_code == 200
        
        search_data = search_response.json()
        if not search_data["results"]:
            pytest.skip("没有搜索结果，跳过缓存测试")
        
        cve_id = search_data["results"][0]["cve_info"]["cve_id"]
        has_summary_before = search_data["results"][0].get("summary") is not None
        
        # 2. 如果没有总结，生成一个
        if not has_summary_before:
            with patch('app.services.llm_service.LLMService.generate_summary') as mock_gen:
                mock_summary = CVESummary(
                    cve_id=cve_id,
                    summary="缓存测试总结",
                    generated_at="2023-01-01T00:00:00.000Z",
                    model_used="deepseek-chat"
                )
                mock_gen.return_value = mock_summary
                
                summary_response = test_client.get(f"/api/v1/summary/{cve_id}")
                assert summary_response.status_code == 200
        
        # 3. 再次搜索，验证缓存
        search_response2 = test_client.get("/api/v1/search?query=buffer&page=1&page_size=1")
        assert search_response2.status_code == 200
        
        search_data2 = search_response2.json()
        if search_data2["results"]:
            cve_id_2 = search_data2["results"][0]["cve_info"]["cve_id"]
            has_summary_after = search_data2["results"][0].get("summary") is not None
            
            if cve_id == cve_id_2:
                # 如果是同一个 CVE，应该有缓存的总结
                assert has_summary_after, "缓存的总结应该存在"
