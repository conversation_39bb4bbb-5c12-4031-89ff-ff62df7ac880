#!/usr/bin/env python3
"""
测试全新 CVE 的完整缓存流程
"""

import requests
import json

def test_new_cve():
    base_url = "http://localhost:8001"
    
    print("🔍 测试全新 CVE 的完整缓存流程")
    print("=" * 50)
    
    # 步骤 1: 搜索一个不同的 CVE
    print("步骤 1: 搜索一个新的 CVE...")
    search_response = requests.get(f"{base_url}/api/v1/search?query=sql+injection&page=1&page_size=1")
    
    if search_response.status_code != 200:
        print(f"❌ 搜索失败: {search_response.status_code}")
        return
    
    search_data = search_response.json()
    if not search_data['results']:
        print("❌ 没有搜索结果")
        return
    
    cve_id = search_data['results'][0]['cve_info']['cve_id']
    has_summary_before = search_data['results'][0].get('summary') is not None
    
    print(f"✅ 找到新的 CVE: {cve_id}")
    print(f"📝 搜索时是否有总结: {'是' if has_summary_before else '否'}")
    
    if has_summary_before:
        print("⚠️ 这个 CVE 已经有缓存的总结，让我们找一个没有总结的")
        # 尝试搜索更多结果
        search_response = requests.get(f"{base_url}/api/v1/search?query=sql+injection&page=1&page_size=5")
        search_data = search_response.json()
        
        # 找一个没有总结的 CVE
        target_cve = None
        for result in search_data['results']:
            if not result.get('summary'):
                target_cve = result
                break
        
        if target_cve:
            cve_id = target_cve['cve_info']['cve_id']
            has_summary_before = False
            print(f"✅ 找到没有总结的 CVE: {cve_id}")
        else:
            print("⚠️ 前5个结果都有总结，继续使用第一个进行测试")
    
    # 步骤 2: 生成总结（如果没有的话）
    if not has_summary_before:
        print(f"\n步骤 2: 为 {cve_id} 生成新总结...")
        summary_response = requests.get(f"{base_url}/api/v1/summary/{cve_id}")
        
        if summary_response.status_code != 200:
            print(f"❌ 总结生成失败: {summary_response.status_code}")
            return
        
        summary_data = summary_response.json()
        print(f"✅ 新总结生成成功!")
        print(f"🤖 模型: {summary_data['model_used']}")
        print(f"📏 总结长度: {len(summary_data['summary'])} 字符")
        print(f"🔑 缓存的 CVE ID: {summary_data['cve_id']}")
        
        if summary_data['cve_id'] != cve_id:
            print(f"❌ CVE ID 不匹配!")
            print(f"   期望: {cve_id}")
            print(f"   实际: {summary_data['cve_id']}")
            return
    else:
        print(f"\n步骤 2: {cve_id} 已有总结，跳过生成")
    
    # 步骤 3: 使用精确 CVE ID 搜索
    print(f"\n步骤 3: 使用精确 CVE ID 搜索...")
    exact_response = requests.get(f"{base_url}/api/v1/search?query={cve_id}&page=1&page_size=1")
    
    if exact_response.status_code != 200:
        print(f"❌ 精确搜索失败: {exact_response.status_code}")
        return
    
    exact_data = exact_response.json()
    if not exact_data['results']:
        print("❌ 精确搜索没有结果")
        return
    
    exact_cve_id = exact_data['results'][0]['cve_info']['cve_id']
    exact_has_summary = exact_data['results'][0].get('summary') is not None
    
    print(f"✅ 精确搜索完成")
    print(f"🔍 找到的 CVE: {exact_cve_id}")
    print(f"📝 是否有总结: {'是' if exact_has_summary else '否'}")
    
    # 步骤 4: 验证一致性
    print(f"\n步骤 4: 验证一致性...")
    
    if exact_cve_id != cve_id:
        print(f"❌ CVE ID 不一致!")
        print(f"   原始: {cve_id}")
        print(f"   精确搜索: {exact_cve_id}")
        return
    
    print(f"✅ CVE ID 一致: {cve_id}")
    
    if not exact_has_summary:
        print(f"❌ 精确搜索时没有找到总结!")
        print("   这说明缓存没有正确工作")
        return
    
    print(f"✅ 精确搜索找到了总结!")
    
    # 步骤 5: 再次调用 /api/v1/summary 验证缓存
    print(f"\n步骤 5: 再次调用 /api/v1/summary 验证缓存...")
    summary2_response = requests.get(f"{base_url}/api/v1/summary/{cve_id}")
    
    if summary2_response.status_code != 200:
        print(f"❌ 第二次总结调用失败: {summary2_response.status_code}")
        return
    
    summary2_data = summary2_response.json()
    print(f"✅ 第二次总结调用成功")
    print(f"🔑 返回的 CVE ID: {summary2_data['cve_id']}")
    
    if summary2_data['cve_id'] != cve_id:
        print(f"❌ 第二次调用 CVE ID 不匹配!")
        return
    
    print(f"✅ 所有测试通过!")
    
    print(f"\n🎉 全新 CVE 缓存流程测试完成!")
    print(f"\n📋 测试总结:")
    print(f"✅ 目标 CVE: {cve_id}")
    print(f"✅ 总结生成: 成功")
    print(f"✅ 精确搜索: 成功")
    print(f"✅ 缓存一致性: 通过")
    print(f"✅ CVE ID 匹配: 通过")

if __name__ == "__main__":
    test_new_cve()
