#!/usr/bin/env python3
"""
测试UI功能的脚本
"""

import requests
import time

def test_main_page():
    """测试主页面是否正常加载"""
    try:
        response = requests.get("http://localhost:8001", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键元素
            checks = [
                ("筛选器按钮", "筛选器" in content and "toggleFilters" in content),
                ("漏洞类型分组", "漏洞类型" in content),
                ("软件类型分组", "软件类型" in content),
                ("开发语言分组", "开发语言" in content),
                ("严重程度分组", "严重程度" in content),
                ("Vue.js", "vue.global.js" in content),
                ("Element Plus", "element-plus" in content),
                ("应用脚本", "/static/js/app.js" in content),
                ("样式文件", "/static/css/style.css" in content)
            ]
            
            print("🔍 主页面检查结果:")
            all_passed = True
            for name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {name}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print("\n🎉 所有检查都通过了!")
            else:
                print("\n⚠️  有些检查失败了，请检查页面内容")
                
            return all_passed
        else:
            print(f"❌ 页面加载失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_static_files():
    """测试静态文件是否正常加载"""
    files = [
        ("/static/js/app.js", "JavaScript文件"),
        ("/static/css/style.css", "CSS文件")
    ]
    
    print("\n📁 静态文件检查:")
    all_passed = True
    
    for path, name in files:
        try:
            response = requests.get(f"http://localhost:8001{path}", timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {name}")
            else:
                print(f"  ❌ {name} - HTTP {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"  ❌ {name} - 错误: {e}")
            all_passed = False
    
    return all_passed

def test_api_endpoints():
    """测试API端点"""
    endpoints = [
        ("/api/v1/health", "健康检查"),
        ("/api/v1/examples", "搜索示例")
    ]
    
    print("\n🔌 API端点检查:")
    all_passed = True
    
    for path, name in endpoints:
        try:
            response = requests.get(f"http://localhost:8001{path}", timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {name}")
            else:
                print(f"  ❌ {name} - HTTP {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"  ❌ {name} - 错误: {e}")
            all_passed = False
    
    return all_passed

def test_filter_search():
    """测试筛选器搜索功能"""
    print("\n🎯 筛选器搜索测试:")
    
    # 测试SQL注入筛选器
    try:
        query = "+sql injection, +sql_injection, +SQLi"
        params = {
            "query": query,
            "page": 1,
            "page_size": 3
        }
        
        response = requests.get("http://localhost:8001/api/v1/search", params=params, timeout=15)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ SQL注入筛选器: 找到 {data['total_results']} 个结果")
        else:
            print(f"  ❌ SQL注入筛选器: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ SQL注入筛选器: 错误 {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🧪 CVE搜索服务UI功能测试")
    print("=" * 50)
    
    # 检查服务是否运行
    try:
        response = requests.get("http://localhost:8001/api/v1/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务未运行或不健康")
            print("请确保服务正在运行: python -m app.main")
            return
    except:
        print("❌ 无法连接到服务")
        print("请确保服务正在运行: python -m app.main")
        return
    
    print("✅ 服务正在运行\n")
    
    # 运行测试
    tests = [
        ("主页面功能", test_main_page),
        ("静态文件", test_static_files),
        ("API端点", test_api_endpoints),
        ("筛选器搜索", test_filter_search)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"🔄 测试 {name}...")
        result = test_func()
        results.append((name, result))
        time.sleep(1)  # 避免请求过快
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试都通过了！筛选器功能应该正常工作。")
        print("\n💡 如果页面上的筛选器仍然不显示，请:")
        print("  1. 刷新浏览器页面 (Ctrl+F5)")
        print("  2. 点击'显示筛选器'按钮")
        print("  3. 检查浏览器控制台是否有JavaScript错误")
        print("  4. 确保所有分组都已展开")
    else:
        print("⚠️  有些测试失败了，请检查相关功能。")

if __name__ == "__main__":
    main()
