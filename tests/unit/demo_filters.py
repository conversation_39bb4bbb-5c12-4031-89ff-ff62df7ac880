#!/usr/bin/env python3
"""
CVE 搜索服务筛选器功能演示脚本
"""

import requests
import json
import time
from urllib.parse import quote

def demo_filter_combinations():
    """演示不同筛选器组合的效果"""
    base_url = "http://localhost:8001"
    
    print("🎯 CVE 搜索服务筛选器功能演示")
    print("=" * 60)
    
    # 演示用例
    demos = [
        {
            "title": "1. 纯筛选器搜索 - SQL注入漏洞",
            "description": "只使用筛选器，不输入任何文本",
            "query": "+sql injection, +sql_injection, +SQLi",
            "filters": ["SQL注入"]
        },
        {
            "title": "2. 组合搜索 - Apache + 代码执行",
            "description": "结合文本搜索和筛选器",
            "query": "apache, +remote code execution, +remote_code_execution, +RCE",
            "filters": ["文本: apache", "筛选器: 代码执行"]
        },
        {
            "title": "3. 多筛选器组合 - PHP + 高危漏洞",
            "description": "选择多个不同分组的筛选器",
            "query": "+php, +high",
            "filters": ["开发语言: PHP", "严重程度: 高危"]
        },
        {
            "title": "4. 复杂组合 - 数据库 + 权限提升 + 中危",
            "description": "多个筛选器分组的复杂组合",
            "query": "+mysql, +mariadb, +postgresql, +mongodb, +privilege escalation, +privilege_escalation, +elevation, +medium",
            "filters": ["软件类型: 数据库", "漏洞类型: 权限提升", "严重程度: 中危"]
        }
    ]
    
    for demo in demos:
        print(f"\n{demo['title']}")
        print(f"📝 {demo['description']}")
        print(f"🏷️  筛选器: {', '.join(demo['filters'])}")
        print(f"🔍 生成查询: {demo['query']}")
        
        try:
            # 发送搜索请求
            params = {
                "query": demo["query"],
                "page": 1,
                "page_size": 3
            }
            
            start_time = time.time()
            response = requests.get(f"{base_url}/api/v1/search", params=params, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 找到 {data['total_results']} 个结果 (耗时: {end_time - start_time:.2f}s)")
                
                # 显示前几个结果
                if data['results']:
                    print("📋 示例结果:")
                    for i, result in enumerate(data['results'][:2], 1):
                        cve_id = result['cve_info']['cve_id']
                        title = result['cve_info'].get('title', '无标题')
                        severity = result['cve_info'].get('severity', '未知')
                        products = result['cve_info'].get('affected_products', [])
                        
                        print(f"   {i}. {cve_id} [{severity}]")
                        if title and title != '无标题':
                            print(f"      标题: {title[:80]}...")
                        if products:
                            print(f"      影响产品: {', '.join(products[:2])}...")
                else:
                    print("📋 无匹配结果")
            else:
                print(f"❌ 搜索失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 60)

def demo_filter_syntax():
    """演示筛选器语法转换"""
    print("\n🔧 筛选器语法转换演示")
    print("=" * 60)
    
    filter_mappings = {
        "缓冲区溢出": "+(heap|stack)_buffer_overflow, +\"heap-buffer-overflow\", +\"stack-buffer-overflow\"",
        "SQL注入": "+sql injection, +sql_injection, +SQLi",
        "代码执行": "+remote code execution, +remote_code_execution, +RCE",
        "文件读取": "+path traversal, +path_traversal, +LFI, +RFI",
        "XSS": "+cross site scripting, +cross-site-scripting, +XSS",
        "CSRF": "+cross site request forgery, +cross-site-request-forgery, +CSRF",
        "权限提升": "+privilege escalation, +privilege_escalation, +elevation",
        "信息泄露": "+information disclosure, +information_disclosure, +data leak",
        "服务器软件": "+apache, +nginx, +tomcat, +iis",
        "数据库": "+mysql, +mariadb, +postgresql, +mongodb",
        "操作系统": "+windows, +linux, +macos, +unix",
        "浏览器": "+chrome, +firefox, +safari, +edge, +webkit"
    }
    
    for filter_name, syntax in filter_mappings.items():
        print(f"🏷️  {filter_name:12} → {syntax}")
    
    print("\n💡 使用说明:")
    print("   • 选择筛选器后，系统会自动生成对应的搜索语法")
    print("   • 多个筛选器会用逗号连接")
    print("   • 用户输入的文本会与筛选器语法合并")
    print("   • 支持同时选择多个分组的多个筛选器")

def main():
    """主函数"""
    # 检查服务状态
    try:
        response = requests.get("http://localhost:8001/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ CVE 搜索服务运行正常")
        else:
            print("⚠️  服务状态异常，但继续演示...")
    except:
        print("❌ 无法连接到服务，请确保服务正在运行")
        print("   启动命令: python -m app.main")
        return
    
    # 演示筛选器语法
    demo_filter_syntax()
    
    # 演示筛选器组合
    demo_filter_combinations()
    
    print("\n🎉 演示完成!")
    print("\n📖 更多信息请查看:")
    print("   • 功能文档: FILTER_FEATURE_DOCUMENTATION.md")
    print("   • 在线界面: http://localhost:8001")

if __name__ == "__main__":
    main()
